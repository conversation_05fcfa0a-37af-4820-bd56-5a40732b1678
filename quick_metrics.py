#!/usr/bin/env python3
"""
Quick Metrics Display for Quantum Trading System
Shows the 3 specific metrics requested:
1. Total Number of Trades
2. Profits Per Day  
3. Total Trade Profits
"""

import pandas as pd
import os
from datetime import datetime

def display_quick_metrics():
    """Display the 3 requested metrics in a clean format"""
    csv_file = "trade_log.csv"
    
    print("=" * 60)
    print("🚀 QUANTUM TRADING SYSTEM - QUICK METRICS")
    print("=" * 60)
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    if not os.path.exists(csv_file):
        print("❌ No trade log file found!")
        print("💡 Start trading to generate data.")
        return
    
    try:
        # Read CSV data
        df = pd.read_csv(csv_file)
        exits = df[df['action'] == 'EXIT']
        
        if exits.empty:
            print("📊 No completed trades found yet.")
            print("💡 Complete some trades to see metrics.")
            return
        
        # 1. TOTAL NUMBER OF TRADES
        total_trades = len(exits)
        print(f"\n1️⃣ TOTAL NUMBER OF TRADES")
        print(f"   📈 {total_trades} completed trades")
        
        # 3. TOTAL TRADE PROFITS (only winning trades)
        wins = exits[exits['trade_outcome'] == 'WIN']
        losses = exits[exits['trade_outcome'] == 'LOSS']
        total_profits = wins['pnl_usdt'].sum() if len(wins) > 0 else 0.0
        total_losses = abs(losses['pnl_usdt'].sum()) if len(losses) > 0 else 0.0
        net_pnl = exits['pnl_usdt'].sum()
        
        print(f"\n3️⃣ TOTAL TRADE PROFITS")
        print(f"   💰 Gross Profits: {total_profits:.4f} USDT")
        print(f"   💸 Total Losses: {total_losses:.4f} USDT")
        print(f"   📊 Net PnL: {net_pnl:.4f} USDT")
        print(f"   🎯 Win Rate: {len(wins)/total_trades*100:.2f}%")
        
        # 2. PROFITS PER DAY
        exits['date'] = pd.to_datetime(exits['timestamp']).dt.date
        daily_profits = wins.groupby('date')['pnl_usdt'].sum()
        daily_losses = losses.groupby('date')['pnl_usdt'].sum()
        daily_net = exits.groupby('date')['pnl_usdt'].sum()
        
        print(f"\n2️⃣ PROFITS PER DAY")
        print(f"   📅 Trading Days: {len(daily_net)}")
        
        if len(daily_profits) > 0:
            print(f"\n   {'Date':<12} {'Profits':<12} {'Losses':<12} {'Net PnL':<12}")
            print(f"   {'-'*50}")
            
            # Show all trading days
            all_dates = sorted(daily_net.index)
            total_daily_profits = 0
            
            for date in all_dates:
                day_profits = daily_profits.get(date, 0)
                day_losses = daily_losses.get(date, 0)
                day_net = daily_net.get(date, 0)
                total_daily_profits += day_profits
                
                print(f"   {str(date):<12} {day_profits:>8.4f} USDT {day_losses:>8.4f} USDT {day_net:>8.4f} USDT")
            
            print(f"   {'-'*50}")
            print(f"   {'TOTALS':<12} {total_daily_profits:>8.4f} USDT {-total_losses:>8.4f} USDT {net_pnl:>8.4f} USDT")
            
            # Daily statistics
            profitable_days = len([p for p in daily_net if p > 0])
            losing_days = len([p for p in daily_net if p < 0])
            
            print(f"\n   📊 Daily Statistics:")
            print(f"   ✅ Profitable Days: {profitable_days}")
            print(f"   ❌ Losing Days: {losing_days}")
            print(f"   📈 Best Day: {daily_net.max():.4f} USDT")
            print(f"   📉 Worst Day: {daily_net.min():.4f} USDT")
            print(f"   📊 Average Day: {daily_net.mean():.4f} USDT")
        else:
            print("   📅 No profitable days yet")
        
        # Summary
        print(f"\n" + "=" * 60)
        print(f"📋 SUMMARY")
        print(f"=" * 60)
        print(f"Total Trades: {total_trades}")
        print(f"Total Profits: {total_profits:.4f} USDT")
        print(f"Net PnL: {net_pnl:.4f} USDT")
        print(f"Trading Days: {len(daily_net)}")
        print(f"Status: {'🟢 Profitable' if net_pnl > 0 else '🔴 Losing' if net_pnl < 0 else '🟡 Breakeven'}")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ Error reading trade data: {e}")

def export_metrics_to_csv():
    """Export the 3 metrics to a simple CSV file"""
    csv_file = "trade_log.csv"
    output_file = "my_trading_metrics.csv"
    
    if not os.path.exists(csv_file):
        print("❌ No trade data to export")
        return
    
    try:
        df = pd.read_csv(csv_file)
        exits = df[df['action'] == 'EXIT']
        
        if exits.empty:
            print("❌ No completed trades to export")
            return
        
        # Calculate metrics
        total_trades = len(exits)
        wins = exits[exits['trade_outcome'] == 'WIN']
        total_profits = wins['pnl_usdt'].sum() if len(wins) > 0 else 0.0
        
        # Daily profits
        exits['date'] = pd.to_datetime(exits['timestamp']).dt.date
        daily_profits = wins.groupby('date')['pnl_usdt'].sum()
        
        # Create export data
        export_data = []
        
        # Add summary metrics
        export_data.append(['Metric', 'Value', 'Unit', 'Date'])
        export_data.append(['Total Number of Trades', total_trades, 'Trades', 'All Time'])
        export_data.append(['Total Trade Profits', f"{total_profits:.4f}", 'USDT', 'All Time'])
        export_data.append(['', '', '', ''])  # Empty row
        export_data.append(['Daily Profits', '', '', ''])
        
        # Add daily profits
        for date, profit in daily_profits.items():
            export_data.append(['Profits Per Day', f"{profit:.4f}", 'USDT', str(date)])
        
        # Write to CSV
        import csv
        with open(output_file, 'w', newline='') as file:
            writer = csv.writer(file)
            writer.writerows(export_data)
        
        print(f"📊 Metrics exported to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error exporting metrics: {e}")

if __name__ == "__main__":
    # Display the metrics
    display_quick_metrics()
    
    # Ask if user wants to export
    print(f"\n💾 Export metrics to CSV? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes']:
            export_metrics_to_csv()
    except:
        pass  # Handle if running in non-interactive environment
    
    print(f"\n🔄 Run this script anytime to check your trading metrics!")
