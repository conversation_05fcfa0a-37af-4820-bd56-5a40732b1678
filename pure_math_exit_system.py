#!/usr/bin/env python3
"""
Pure Mathematical/Quantitative Exit System
NO Technical Indicators - Pure Quant/Physics/Engineering/Finance/Maths
Based on user's 2-year experience feedback
"""

import numpy as np
from datetime import datetime
from enum import Enum

class ExitSignal(Enum):
    HOLD = "HOLD"
    EXIT_PROFIT = "EXIT_PROFIT"
    EXIT_LOSS = "EXIT_LOSS"
    EXIT_TIME = "EXIT_TIME"

class PureMathExitSystem:
    """
    Pure Mathematical Exit System
    NO Technical Indicators - Only Mathematical/Quantitative Analysis
    """
    
    def __init__(self):
        # Pure mathematical parameters
        self.profit_threshold = 0.02  # 2% minimum profit to consider exit
        self.loss_threshold = 0.015   # 1.5% maximum loss before exit
        self.volatility_window = 20   # Window for volatility calculation
        self.momentum_window = 10     # Window for momentum calculation
        self.max_hold_periods = 50    # Maximum periods to hold
        
    def calculate_price_momentum(self, prices):
        """
        Pure mathematical momentum calculation
        Based on price velocity and acceleration
        """
        if len(prices) < 3:
            return 0.0
            
        # Calculate price velocity (first derivative)
        velocity = np.diff(prices)
        
        # Calculate price acceleration (second derivative)
        acceleration = np.diff(velocity) if len(velocity) > 1 else np.array([0])
        
        # Recent momentum score
        recent_velocity = np.mean(velocity[-5:]) if len(velocity) >= 5 else velocity[-1]
        recent_acceleration = np.mean(acceleration[-3:]) if len(acceleration) >= 3 else acceleration[-1]
        
        # Momentum score: combination of velocity and acceleration
        momentum_score = recent_velocity + (0.5 * recent_acceleration)
        
        return momentum_score
    
    def calculate_volatility_regime(self, prices):
        """
        Mathematical volatility calculation
        Using standard deviation and variance analysis
        """
        if len(prices) < self.volatility_window:
            return np.std(prices) if len(prices) > 1 else 0.0
            
        # Calculate rolling returns
        returns = np.diff(prices) / prices[:-1]
        
        # Current volatility
        current_vol = np.std(returns[-self.volatility_window:])
        
        # Historical volatility
        historical_vol = np.std(returns)
        
        # Volatility regime: current vs historical
        vol_ratio = current_vol / historical_vol if historical_vol > 0 else 1.0
        
        return vol_ratio
    
    def calculate_price_efficiency(self, prices):
        """
        Mathematical price efficiency calculation
        Measures how efficiently price moves in one direction
        """
        if len(prices) < 2:
            return 0.0
            
        # Total price movement (sum of absolute changes)
        total_movement = np.sum(np.abs(np.diff(prices)))
        
        # Net price movement (direct distance)
        net_movement = abs(prices[-1] - prices[0])
        
        # Efficiency ratio
        efficiency = net_movement / total_movement if total_movement > 0 else 0.0
        
        return efficiency
    
    def calculate_profit_velocity(self, prices, entry_price, position_type):
        """
        Mathematical profit velocity calculation
        How fast profits are accumulating
        """
        if len(prices) < 2:
            return 0.0
            
        current_price = prices[-1]
        
        # Calculate current profit
        if position_type.upper() == "BUY":
            current_profit = (current_price - entry_price) / entry_price
        else:  # SELL
            current_profit = (entry_price - current_price) / entry_price
            
        # Calculate profit changes over time
        profit_history = []
        for price in prices[-10:]:  # Last 10 periods
            if position_type.upper() == "BUY":
                profit = (price - entry_price) / entry_price
            else:
                profit = (entry_price - price) / entry_price
            profit_history.append(profit)
        
        # Profit velocity (rate of profit change)
        if len(profit_history) >= 2:
            profit_velocity = np.mean(np.diff(profit_history))
        else:
            profit_velocity = 0.0
            
        return profit_velocity, current_profit
    
    def calculate_mathematical_exit_signal(self, prices, entry_price, entry_time, 
                                         position_type, periods_held=0):
        """
        Pure mathematical exit signal calculation
        NO technical indicators - only mathematical analysis
        """
        if len(prices) < 5:
            return {
                'signal': ExitSignal.HOLD,
                'confidence': 0.0,
                'reason': 'INSUFFICIENT_DATA',
                'current_profit': 0.0,
                'recommended_action': 'HOLD'
            }
        
        current_price = prices[-1]
        
        # 1. Calculate current profit/loss
        if position_type.upper() == "BUY":
            current_profit = (current_price - entry_price) / entry_price
        else:  # SELL
            current_profit = (entry_price - current_price) / entry_price
        
        # 2. Mathematical momentum analysis
        momentum = self.calculate_price_momentum(prices)
        
        # 3. Volatility regime analysis
        volatility_ratio = self.calculate_volatility_regime(prices)
        
        # 4. Price efficiency analysis
        efficiency = self.calculate_price_efficiency(prices)
        
        # 5. Profit velocity analysis
        profit_velocity, _ = self.calculate_profit_velocity(prices, entry_price, position_type)
        
        # 6. Mathematical exit logic
        exit_signal = ExitSignal.HOLD
        confidence = 0.0
        reason = "CONTINUE_HOLDING"
        
        # Exit conditions based on pure mathematics
        
        # Condition 1: Profit target reached with slowing momentum
        if current_profit >= self.profit_threshold:
            if momentum < 0 and profit_velocity < 0:  # Momentum turning negative
                exit_signal = ExitSignal.EXIT_PROFIT
                confidence = 0.8
                reason = "PROFIT_TARGET_MOMENTUM_DECLINE"
        
        # Condition 2: Loss threshold exceeded
        if current_profit <= -self.loss_threshold:
            exit_signal = ExitSignal.EXIT_LOSS
            confidence = 0.9
            reason = "LOSS_THRESHOLD_EXCEEDED"
        
        # Condition 3: High volatility with low efficiency (choppy market)
        if volatility_ratio > 1.5 and efficiency < 0.3:
            if current_profit > 0:  # Only exit if profitable
                exit_signal = ExitSignal.EXIT_PROFIT
                confidence = 0.7
                reason = "HIGH_VOLATILITY_LOW_EFFICIENCY"
        
        # Condition 4: Momentum reversal with profit
        if current_profit > 0.01:  # At least 1% profit
            if position_type.upper() == "BUY" and momentum < -0.001:
                exit_signal = ExitSignal.EXIT_PROFIT
                confidence = 0.75
                reason = "MOMENTUM_REVERSAL_WITH_PROFIT"
            elif position_type.upper() == "SELL" and momentum > 0.001:
                exit_signal = ExitSignal.EXIT_PROFIT
                confidence = 0.75
                reason = "MOMENTUM_REVERSAL_WITH_PROFIT"
        
        # Condition 5: Time-based exit
        if periods_held >= self.max_hold_periods:
            exit_signal = ExitSignal.EXIT_TIME
            confidence = 0.6
            reason = "TIME_BASED_EXIT"
        
        # Condition 6: Profit velocity turning negative with decent profit
        if current_profit > 0.015 and profit_velocity < -0.002:
            exit_signal = ExitSignal.EXIT_PROFIT
            confidence = 0.8
            reason = "PROFIT_VELOCITY_DECLINE"
        
        return {
            'signal': exit_signal,
            'confidence': confidence,
            'reason': reason,
            'current_profit': current_profit,
            'momentum': momentum,
            'volatility_ratio': volatility_ratio,
            'efficiency': efficiency,
            'profit_velocity': profit_velocity,
            'recommended_action': exit_signal.value,
            'mathematical_metrics': {
                'momentum_score': momentum,
                'volatility_regime': volatility_ratio,
                'price_efficiency': efficiency,
                'profit_velocity': profit_velocity,
                'periods_held': periods_held
            }
        }

def analyze_pure_math_exit(prices, entry_price, entry_time, position_type, periods_held=0):
    """
    Main function for pure mathematical exit analysis
    NO technical indicators - only mathematical/quantitative analysis
    """
    system = PureMathExitSystem()
    
    result = system.calculate_mathematical_exit_signal(
        prices=prices,
        entry_price=entry_price,
        entry_time=entry_time,
        position_type=position_type,
        periods_held=periods_held
    )
    
    # Convert to format compatible with main system
    should_exit = result['signal'] != ExitSignal.HOLD
    
    return {
        'should_exit': should_exit,
        'exit_reason': result['reason'],
        'current_profit_pct': result['current_profit'],
        'confidence': result['confidence'],
        'recommended_exit_price': prices[-1] if should_exit else None,
        'mathematical_analysis': result['mathematical_metrics'],
        'pure_math_signal': result['signal'].value
    }

if __name__ == "__main__":
    # Test the pure mathematical system
    print("🧮 PURE MATHEMATICAL EXIT SYSTEM")
    print("=" * 50)
    print("✅ NO Technical Indicators")
    print("✅ Pure Quant/Physics/Engineering/Finance/Maths")
    print("✅ Based on 2-year experience feedback")
    print("=" * 50)
    
    # Test with sample data
    test_prices = [100, 101, 102, 103, 102.5, 104, 105, 104.5, 106, 105.5]
    
    result = analyze_pure_math_exit(
        prices=test_prices,
        entry_price=100,
        entry_time=datetime.now(),
        position_type="BUY",
        periods_held=10
    )
    
    print(f"📊 Test Result:")
    print(f"   Should Exit: {result['should_exit']}")
    print(f"   Exit Reason: {result['exit_reason']}")
    print(f"   Current Profit: {result['current_profit_pct']:.2%}")
    print(f"   Confidence: {result['confidence']:.2f}")
    print(f"   Signal: {result['pure_math_signal']}")
    
    print(f"\n🧮 Mathematical Metrics:")
    metrics = result['mathematical_analysis']
    print(f"   Momentum Score: {metrics['momentum_score']:.4f}")
    print(f"   Volatility Regime: {metrics['volatility_regime']:.2f}")
    print(f"   Price Efficiency: {metrics['price_efficiency']:.2f}")
    print(f"   Profit Velocity: {metrics['profit_velocity']:.4f}")
    
    print(f"\n✅ Pure Mathematical System Ready!")
