#================================================================================================================================
# trade_state.py
import json
import os
from datetime import datetime

TRADE_STATE_FILE = "trade_state.json"

# Ensure that the trade state is saved and loaded correctly
def save_trade_state(state):
    with open(TRADE_STATE_FILE, 'w') as file:
        json.dump(state, file, indent=2)
    print(f"Trade state saved: {state}")

def load_trade_state():
    if not os.path.exists(TRADE_STATE_FILE):
        print("Trade state file does not exist. Returning empty state.")
        return {}
    with open(TRADE_STATE_FILE, 'r') as file:
        content = file.read().strip()
        if not content:
            print("Trade state file is empty. Returning empty state.")
            return {}
        return json.loads(content)


def update_trade(asset_symbol, status, entry_price=None, amount=None, entry_time=None):
    state = load_trade_state()

    if status == "OPEN":
        state[asset_symbol] = {
            "status": "OPEN",
            "entry_price": entry_price,
            "amount": amount,
            "entry_time": entry_time.strftime("%Y-%m-%d %H:%M:%S"),
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
    elif status == "CLOSED":
        if asset_symbol in state:
            state[asset_symbol]["status"] = "CLOSED"
            state[asset_symbol]["close_time"] = datetime.utcnow().isoformat() + "Z"

    save_trade_state(state)
    print(f"Trade state updated for {asset_symbol}: {state[asset_symbol]}")

def get_position_status(asset_symbol):
    state = load_trade_state()
    return state.get(asset_symbol, {}).get("status", "NONE")

def is_position_open(asset_symbol):
    return get_position_status(asset_symbol) == "OPEN"

def get_trade_details(asset_symbol):
    state = load_trade_state()
    return state.get(asset_symbol, {})


def load_trade(asset_symbol):
    """
    Load trade details for a given asset symbol.

    Args:
        asset_symbol (str): The symbol of the asset.

    Returns:
        dict: The trade details for the asset.
    """
    return get_trade_details(asset_symbol)


#=================================================================================================================
# And also have printed the trade history in the trade_logs.json

