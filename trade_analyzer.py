#!/usr/bin/env python3
"""
Trade Performance Analyzer for Quantum Trading System
Analyzes the trade_log.csv file and provides comprehensive trading statistics
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def analyze_trading_performance(csv_file="trade_log.csv"):
    """Analyze trading performance from CSV log file"""
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file '{csv_file}' not found!")
        print("Make sure to run some trades first to generate the log file.")
        return
    
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        
        if df.empty:
            print("📊 No trades found in the CSV file yet.")
            return
        
        print("=" * 80)
        print("🚀 QUANTUM TRADING SYSTEM - PERFORMANCE ANALYSIS")
        print("=" * 80)
        
        # Basic Statistics
        total_trades = len(df[df['action'] == 'EXIT'])
        total_entries = len(df[df['action'] == 'ENTRY'])
        
        print(f"\n📈 BASIC STATISTICS:")
        print(f"Total Trade Entries: {total_entries}")
        print(f"Total Trade Exits: {total_trades}")
        print(f"Open Trades: {total_entries - total_trades}")

        # 1. TOTAL NUMBER OF TRADES (as requested)
        print(f"\n🔢 TOTAL NUMBER OF TRADES: {total_trades}")
        
        if total_trades == 0:
            print("⏳ No completed trades yet. Run the system to generate trade data.")
            return
        
        # Filter only completed trades (exits)
        exits = df[df['action'] == 'EXIT'].copy()
        
        # Win/Loss Analysis
        wins = exits[exits['trade_outcome'] == 'WIN']
        losses = exits[exits['trade_outcome'] == 'LOSS']
        
        win_count = len(wins)
        loss_count = len(losses)
        win_rate = (win_count / total_trades) * 100 if total_trades > 0 else 0
        
        print(f"\n🎯 WIN/LOSS ANALYSIS:")
        print(f"Winning Trades: {win_count}")
        print(f"Losing Trades: {loss_count}")
        print(f"Win Rate: {win_rate:.2f}%")
        
        # PnL Analysis
        total_pnl = exits['pnl_usdt'].sum()
        avg_win = wins['pnl_usdt'].mean() if len(wins) > 0 else 0
        avg_loss = losses['pnl_usdt'].mean() if len(losses) > 0 else 0
        
        print(f"\n💰 PROFIT & LOSS ANALYSIS:")
        print(f"Total PnL: {total_pnl:.4f} USDT")
        print(f"Average Win: {avg_win:.4f} USDT")
        print(f"Average Loss: {avg_loss:.4f} USDT")
        print(f"Profit Factor: {abs(avg_win / avg_loss):.2f}" if avg_loss != 0 else "N/A")

        # 3. TOTAL TRADE PROFITS (as requested)
        total_profits_only = wins['pnl_usdt'].sum() if len(wins) > 0 else 0
        print(f"\n💵 TOTAL TRADE PROFITS: {total_profits_only:.4f} USDT")
        print(f"Total Trade Losses: {losses['pnl_usdt'].sum():.4f} USDT" if len(losses) > 0 else "Total Trade Losses: 0.0000 USDT")

        # 4. TRADE PnL IN PERCENTAGE (as requested)
        avg_win_pct = wins['pnl_pct'].mean() if len(wins) > 0 else 0
        avg_loss_pct = losses['pnl_pct'].mean() if len(losses) > 0 else 0
        total_pnl_pct = exits['pnl_pct'].mean() if len(exits) > 0 else 0

        print(f"\n📊 TRADE PnL IN PERCENTAGE:")
        print(f"Average Win: {avg_win_pct:.2f}%")
        print(f"Average Loss: {avg_loss_pct:.2f}%")
        print(f"Average Trade PnL: {total_pnl_pct:.2f}%")
        print(f"Best Trade: {exits['pnl_pct'].max():.2f}%" if len(exits) > 0 else "Best Trade: 0.00%")
        print(f"Worst Trade: {exits['pnl_pct'].min():.2f}%" if len(exits) > 0 else "Worst Trade: 0.00%")
        
        # Risk-Reward Analysis
        avg_risk_reward = exits['actual_risk_reward_ratio'].mean()
        print(f"Average Risk-Reward Ratio: {avg_risk_reward:.2f}")
        
        # Trade Duration Analysis
        avg_duration = exits['trade_duration_minutes'].mean()
        print(f"Average Trade Duration: {avg_duration:.2f} minutes")
        
        # Asset Performance
        print(f"\n📊 ASSET PERFORMANCE:")
        asset_performance = exits.groupby('asset').agg({
            'pnl_usdt': ['count', 'sum', 'mean'],
            'trade_outcome': lambda x: (x == 'WIN').sum() / len(x) * 100
        }).round(4)
        
        asset_performance.columns = ['Trades', 'Total_PnL', 'Avg_PnL', 'Win_Rate_%']
        print(asset_performance)
        
        # Exit Reason Analysis
        print(f"\n🚪 EXIT REASON ANALYSIS:")
        exit_reasons = exits['exit_reason'].value_counts()
        for reason, count in exit_reasons.items():
            percentage = (count / total_trades) * 100
            print(f"{reason}: {count} trades ({percentage:.1f}%)")
        
        # Signal Type Performance
        print(f"\n📡 SIGNAL TYPE PERFORMANCE:")
        signal_performance = exits.groupby('signal_type').agg({
            'pnl_usdt': ['count', 'sum', 'mean'],
            'trade_outcome': lambda x: (x == 'WIN').sum() / len(x) * 100
        }).round(4)
        
        signal_performance.columns = ['Trades', 'Total_PnL', 'Avg_PnL', 'Win_Rate_%']
        print(signal_performance)
        
        # Recent Performance (Last 10 trades)
        print(f"\n🕒 RECENT PERFORMANCE (Last 10 trades):")
        recent_trades = exits.tail(10)[['timestamp', 'asset', 'signal_type', 'pnl_usdt', 'trade_outcome', 'exit_reason']]
        print(recent_trades.to_string(index=False))
        
        # Performance Trends
        exits['date'] = pd.to_datetime(exits['timestamp']).dt.date
        daily_performance = exits.groupby('date')['pnl_usdt'].sum()

        # 2. PROFITS PER DAY (as requested) - Enhanced with Percentage
        print(f"\n📅 PROFITS PER DAY:")
        daily_profits_only = exits[exits['trade_outcome'] == 'WIN'].groupby('date')['pnl_usdt'].sum()
        daily_losses_only = exits[exits['trade_outcome'] == 'LOSS'].groupby('date')['pnl_usdt'].sum()
        daily_profits_pct = exits[exits['trade_outcome'] == 'WIN'].groupby('date')['pnl_pct'].sum()
        daily_losses_pct = exits[exits['trade_outcome'] == 'LOSS'].groupby('date')['pnl_pct'].sum()

        # Create comprehensive daily breakdown
        all_dates = sorted(set(daily_profits_only.index) | set(daily_losses_only.index))

        print(f"{'Date':<12} {'Profits USDT':<14} {'Losses USDT':<13} {'Net PnL USDT':<13} {'Net PnL %':<10} {'Trades':<8}")
        print("-" * 80)

        total_daily_profits = 0
        total_daily_profits_pct = 0
        for date in all_dates:
            day_profits = daily_profits_only.get(date, 0)
            day_losses = daily_losses_only.get(date, 0)
            day_net = day_profits + day_losses  # losses are already negative
            day_trades = len(exits[exits['date'] == date])

            # Calculate percentage for the day
            day_profits_pct = daily_profits_pct.get(date, 0)
            day_losses_pct = daily_losses_pct.get(date, 0)
            day_net_pct = day_profits_pct + day_losses_pct

            total_daily_profits += day_profits
            total_daily_profits_pct += day_profits_pct

            print(f"{str(date):<12} {day_profits:>10.4f} USDT {day_losses:>9.4f} USDT {day_net:>9.4f} USDT {day_net_pct:>7.2f}% {day_trades:>4}")

        print("-" * 80)
        print(f"{'TOTAL':<12} {total_daily_profits:>10.4f} USDT")

        print(f"\n📊 DAILY SUMMARY:")
        profitable_days = len([p for p in daily_performance if p > 0])
        losing_days = len([p for p in daily_performance if p < 0])
        breakeven_days = len([p for p in daily_performance if p == 0])

        print(f"Profitable Days: {profitable_days}")
        print(f"Losing Days: {losing_days}")
        print(f"Breakeven Days: {breakeven_days}")
        print(f"Best Day: {daily_performance.max():.4f} USDT" if len(daily_performance) > 0 else "Best Day: N/A")
        print(f"Worst Day: {daily_performance.min():.4f} USDT" if len(daily_performance) > 0 else "Worst Day: N/A")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if win_rate < 50:
            print("⚠️  Win rate is below 50%. Consider tightening entry criteria.")
        elif win_rate > 70:
            print("✅ Excellent win rate! System is performing well.")
        
        if total_pnl < 0:
            print("⚠️  Overall PnL is negative. Review risk management settings.")
        else:
            print("✅ Positive overall PnL. Good job!")
        
        if avg_risk_reward < 1.5:
            print("⚠️  Risk-reward ratio is low. Consider adjusting TP/SL levels.")
        else:
            print("✅ Good risk-reward ratio.")
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"❌ Error analyzing trades: {e}")

def export_requested_metrics(csv_file="trade_log.csv", output_file="trading_metrics.csv"):
    """Export the 3 specific metrics requested by user to a separate CSV"""
    try:
        df = pd.read_csv(csv_file)
        exits = df[df['action'] == 'EXIT']

        if exits.empty:
            print("⚠️ No completed trades found for metrics export.")
            return

        # 1. Total Number of Trades
        total_trades = len(exits)

        # 2. Profits Per Day
        exits['date'] = pd.to_datetime(exits['timestamp']).dt.date
        daily_profits = exits[exits['trade_outcome'] == 'WIN'].groupby('date')['pnl_usdt'].sum()

        # 3. Total Trade Profits
        total_profits = exits[exits['trade_outcome'] == 'WIN']['pnl_usdt'].sum()

        # Create metrics summary
        metrics_data = []

        # Add total trades metric
        metrics_data.append({
            'Metric': 'Total Number of Trades',
            'Value': total_trades,
            'Unit': 'Trades',
            'Date': 'All Time'
        })

        # Add total profits metric
        metrics_data.append({
            'Metric': 'Total Trade Profits',
            'Value': round(total_profits, 4),
            'Unit': 'USDT',
            'Date': 'All Time'
        })

        # Add percentage metrics
        avg_win_pct = exits[exits['trade_outcome'] == 'WIN']['pnl_pct'].mean() if len(exits[exits['trade_outcome'] == 'WIN']) > 0 else 0
        avg_loss_pct = exits[exits['trade_outcome'] == 'LOSS']['pnl_pct'].mean() if len(exits[exits['trade_outcome'] == 'LOSS']) > 0 else 0
        avg_trade_pct = exits['pnl_pct'].mean() if len(exits) > 0 else 0

        metrics_data.append({
            'Metric': 'Average Win Percentage',
            'Value': round(avg_win_pct, 2),
            'Unit': '%',
            'Date': 'All Time'
        })

        metrics_data.append({
            'Metric': 'Average Loss Percentage',
            'Value': round(avg_loss_pct, 2),
            'Unit': '%',
            'Date': 'All Time'
        })

        metrics_data.append({
            'Metric': 'Average Trade PnL Percentage',
            'Value': round(avg_trade_pct, 2),
            'Unit': '%',
            'Date': 'All Time'
        })

        # Add daily profits
        for date, profit in daily_profits.items():
            metrics_data.append({
                'Metric': 'Profits Per Day',
                'Value': round(profit, 4),
                'Unit': 'USDT',
                'Date': str(date)
            })

        # Create DataFrame and save to CSV
        metrics_df = pd.DataFrame(metrics_data)
        metrics_df.to_csv(output_file, index=False)

        print(f"📊 Requested metrics exported to: {output_file}")
        print(f"✅ Total Number of Trades: {total_trades}")
        print(f"✅ Total Trade Profits: {total_profits:.4f} USDT")
        print(f"✅ Profits Per Day: {len(daily_profits)} days of data exported")

    except Exception as e:
        print(f"❌ Error exporting metrics: {e}")

def export_summary_report(csv_file="trade_log.csv", output_file="trading_summary.txt"):
    """Export a summary report to a text file"""
    try:
        df = pd.read_csv(csv_file)
        exits = df[df['action'] == 'EXIT']
        
        with open(output_file, 'w') as f:
            f.write("QUANTUM TRADING SYSTEM - PERFORMANCE SUMMARY\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            total_trades = len(exits)
            wins = len(exits[exits['trade_outcome'] == 'WIN'])
            win_rate = (wins / total_trades) * 100 if total_trades > 0 else 0
            total_pnl = exits['pnl_usdt'].sum()
            
            f.write(f"Total Trades: {total_trades}\n")
            f.write(f"Win Rate: {win_rate:.2f}%\n")
            f.write(f"Total PnL: {total_pnl:.4f} USDT\n")
            f.write(f"Average Trade Duration: {exits['trade_duration_minutes'].mean():.2f} minutes\n")
        
        print(f"📄 Summary report exported to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error exporting summary: {e}")

if __name__ == "__main__":
    # Run the analysis
    analyze_trading_performance()

    # Export the 3 specific metrics requested
    export_requested_metrics()

    # Export summary report
    export_summary_report()

    print("\n🔄 Run this script anytime to analyze your trading performance!")
    print("📁 Main CSV file: trade_log.csv")
    print("📁 Metrics CSV file: trading_metrics.csv")
    print("📁 Summary report: trading_summary.txt")
