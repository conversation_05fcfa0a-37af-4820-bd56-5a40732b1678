#!/usr/bin/env python3
"""
Trade Performance Analyzer for Quantum Trading System
Analyzes the trade_log.csv file and provides comprehensive trading statistics
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os

def analyze_trading_performance(csv_file="trade_log.csv"):
    """Analyze trading performance from CSV log file"""
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file '{csv_file}' not found!")
        print("Make sure to run some trades first to generate the log file.")
        return
    
    try:
        # Read the CSV file
        df = pd.read_csv(csv_file)
        
        if df.empty:
            print("📊 No trades found in the CSV file yet.")
            return
        
        print("=" * 80)
        print("🚀 QUANTUM TRADING SYSTEM - PERFORMANCE ANALYSIS")
        print("=" * 80)
        
        # Basic Statistics
        total_trades = len(df[df['action'] == 'EXIT'])
        total_entries = len(df[df['action'] == 'ENTRY'])
        
        print(f"\n📈 BASIC STATISTICS:")
        print(f"Total Trade Entries: {total_entries}")
        print(f"Total Trade Exits: {total_trades}")
        print(f"Open Trades: {total_entries - total_trades}")
        
        if total_trades == 0:
            print("⏳ No completed trades yet. Run the system to generate trade data.")
            return
        
        # Filter only completed trades (exits)
        exits = df[df['action'] == 'EXIT'].copy()
        
        # Win/Loss Analysis
        wins = exits[exits['trade_outcome'] == 'WIN']
        losses = exits[exits['trade_outcome'] == 'LOSS']
        
        win_count = len(wins)
        loss_count = len(losses)
        win_rate = (win_count / total_trades) * 100 if total_trades > 0 else 0
        
        print(f"\n🎯 WIN/LOSS ANALYSIS:")
        print(f"Winning Trades: {win_count}")
        print(f"Losing Trades: {loss_count}")
        print(f"Win Rate: {win_rate:.2f}%")
        
        # PnL Analysis
        total_pnl = exits['pnl_usdt'].sum()
        avg_win = wins['pnl_usdt'].mean() if len(wins) > 0 else 0
        avg_loss = losses['pnl_usdt'].mean() if len(losses) > 0 else 0
        
        print(f"\n💰 PROFIT & LOSS ANALYSIS:")
        print(f"Total PnL: {total_pnl:.4f} USDT")
        print(f"Average Win: {avg_win:.4f} USDT")
        print(f"Average Loss: {avg_loss:.4f} USDT")
        print(f"Profit Factor: {abs(avg_win / avg_loss):.2f}" if avg_loss != 0 else "N/A")
        
        # Risk-Reward Analysis
        avg_risk_reward = exits['actual_risk_reward_ratio'].mean()
        print(f"Average Risk-Reward Ratio: {avg_risk_reward:.2f}")
        
        # Trade Duration Analysis
        avg_duration = exits['trade_duration_minutes'].mean()
        print(f"Average Trade Duration: {avg_duration:.2f} minutes")
        
        # Asset Performance
        print(f"\n📊 ASSET PERFORMANCE:")
        asset_performance = exits.groupby('asset').agg({
            'pnl_usdt': ['count', 'sum', 'mean'],
            'trade_outcome': lambda x: (x == 'WIN').sum() / len(x) * 100
        }).round(4)
        
        asset_performance.columns = ['Trades', 'Total_PnL', 'Avg_PnL', 'Win_Rate_%']
        print(asset_performance)
        
        # Exit Reason Analysis
        print(f"\n🚪 EXIT REASON ANALYSIS:")
        exit_reasons = exits['exit_reason'].value_counts()
        for reason, count in exit_reasons.items():
            percentage = (count / total_trades) * 100
            print(f"{reason}: {count} trades ({percentage:.1f}%)")
        
        # Signal Type Performance
        print(f"\n📡 SIGNAL TYPE PERFORMANCE:")
        signal_performance = exits.groupby('signal_type').agg({
            'pnl_usdt': ['count', 'sum', 'mean'],
            'trade_outcome': lambda x: (x == 'WIN').sum() / len(x) * 100
        }).round(4)
        
        signal_performance.columns = ['Trades', 'Total_PnL', 'Avg_PnL', 'Win_Rate_%']
        print(signal_performance)
        
        # Recent Performance (Last 10 trades)
        print(f"\n🕒 RECENT PERFORMANCE (Last 10 trades):")
        recent_trades = exits.tail(10)[['timestamp', 'asset', 'signal_type', 'pnl_usdt', 'trade_outcome', 'exit_reason']]
        print(recent_trades.to_string(index=False))
        
        # Performance Trends
        exits['date'] = pd.to_datetime(exits['timestamp']).dt.date
        daily_performance = exits.groupby('date')['pnl_usdt'].sum()
        
        print(f"\n📅 DAILY PERFORMANCE:")
        for date, pnl in daily_performance.items():
            print(f"{date}: {pnl:.4f} USDT")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if win_rate < 50:
            print("⚠️  Win rate is below 50%. Consider tightening entry criteria.")
        elif win_rate > 70:
            print("✅ Excellent win rate! System is performing well.")
        
        if total_pnl < 0:
            print("⚠️  Overall PnL is negative. Review risk management settings.")
        else:
            print("✅ Positive overall PnL. Good job!")
        
        if avg_risk_reward < 1.5:
            print("⚠️  Risk-reward ratio is low. Consider adjusting TP/SL levels.")
        else:
            print("✅ Good risk-reward ratio.")
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"❌ Error analyzing trades: {e}")

def export_summary_report(csv_file="trade_log.csv", output_file="trading_summary.txt"):
    """Export a summary report to a text file"""
    try:
        df = pd.read_csv(csv_file)
        exits = df[df['action'] == 'EXIT']
        
        with open(output_file, 'w') as f:
            f.write("QUANTUM TRADING SYSTEM - PERFORMANCE SUMMARY\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            total_trades = len(exits)
            wins = len(exits[exits['trade_outcome'] == 'WIN'])
            win_rate = (wins / total_trades) * 100 if total_trades > 0 else 0
            total_pnl = exits['pnl_usdt'].sum()
            
            f.write(f"Total Trades: {total_trades}\n")
            f.write(f"Win Rate: {win_rate:.2f}%\n")
            f.write(f"Total PnL: {total_pnl:.4f} USDT\n")
            f.write(f"Average Trade Duration: {exits['trade_duration_minutes'].mean():.2f} minutes\n")
        
        print(f"📄 Summary report exported to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error exporting summary: {e}")

if __name__ == "__main__":
    # Run the analysis
    analyze_trading_performance()
    
    # Export summary report
    export_summary_report()
    
    print("\n🔄 Run this script anytime to analyze your trading performance!")
    print("📁 CSV file location: trade_log.csv")
