#!/usr/bin/env python3
"""
🛡️ SECURE TRADING SYSTEM LAUNCHER
Launches your quantum trading system with maximum security protection
Protects against hackers, unauthorized access, and data theft
"""

import os
import sys
import getpass
import subprocess
from datetime import datetime
from trading_security_system import initialize_trading_security, TradingSecurityManager

print("🛡️ SECURE QUANTUM TRADING LAUNCHER")
print("🔒 Maximum Protection Against Hackers")
print("=" * 60)

def secure_input(prompt: str, hide_input: bool = False) -> str:
    """Secure input with validation"""
    if hide_input:
        return getpass.getpass(prompt)
    else:
        return input(prompt).strip()

def validate_api_key(api_key: str) -> bool:
    """Validate API key format"""
    return len(api_key) >= 32 and api_key.isalnum()

def validate_api_secret(api_secret: str) -> bool:
    """Validate API secret format"""
    return len(api_secret) >= 32

def check_system_security():
    """Check if system is already secured"""
    security_files = ['.encrypted_credentials', '.security_salt', 'security_log.json']
    return all(os.path.exists(f) for f in security_files)

def main():
    """Main secure launcher"""
    print("🚨 SECURITY CHECK: Scanning for threats...")
    
    # Check if already secured
    if check_system_security():
        print("✅ System security files detected")
        
        # Ask for master password to decrypt
        master_password = secure_input("🔑 Enter your master password to unlock trading system: ", hide_input=True)
        
        # Initialize security manager
        security_manager = TradingSecurityManager()
        
        # Try to decrypt credentials
        api_key, api_secret = security_manager.decrypt_api_credentials(master_password)
        
        if api_key and api_secret:
            print("✅ Credentials decrypted successfully")
            print("🚨 Starting security monitoring...")
            security_manager.start_security_monitoring()
            
            # Launch trading system
            print("🚀 Launching secure trading system...")
            
            # Set environment variables for the trading system
            os.environ['BINANCE_API_KEY'] = api_key
            os.environ['BINANCE_API_SECRET'] = api_secret
            os.environ['SECURITY_ENABLED'] = 'TRUE'
            
            # Launch the main trading system
            try:
                subprocess.run([sys.executable, "quantum Dogi Trade V2.0.py"], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Trading system error: {e}")
            except KeyboardInterrupt:
                print("\n🛑 Trading system stopped by user")
            finally:
                # Clear sensitive environment variables
                os.environ.pop('BINANCE_API_KEY', None)
                os.environ.pop('BINANCE_API_SECRET', None)
                print("🔒 Credentials cleared from memory")
        else:
            print("❌ Failed to decrypt credentials - incorrect password")
            sys.exit(1)
    
    else:
        print("⚠️  First-time setup required")
        print("🔒 Setting up maximum security protection...")
        
        # Get API credentials
        print("\n📋 STEP 1: API CREDENTIALS")
        print("🔑 Enter your Binance API credentials (will be encrypted)")
        
        while True:
            api_key = secure_input("Binance API Key: ")
            if validate_api_key(api_key):
                break
            print("❌ Invalid API key format. Must be at least 32 characters.")
        
        while True:
            api_secret = secure_input("Binance API Secret: ", hide_input=True)
            if validate_api_secret(api_secret):
                break
            print("❌ Invalid API secret format. Must be at least 32 characters.")
        
        # Get master password
        print("\n🔐 STEP 2: MASTER PASSWORD")
        print("🔑 Create a strong master password to encrypt your system")
        
        while True:
            master_password = secure_input("Create master password (min 12 characters): ", hide_input=True)
            if len(master_password) >= 12:
                confirm_password = secure_input("Confirm master password: ", hide_input=True)
                if master_password == confirm_password:
                    break
                else:
                    print("❌ Passwords don't match. Try again.")
            else:
                print("❌ Password too short. Must be at least 12 characters.")
        
        # Initialize security
        print("\n🛡️ STEP 3: INITIALIZING SECURITY")
        security_manager = initialize_trading_security(api_key, api_secret, master_password)
        
        if security_manager:
            print("\n✅ SECURITY SETUP COMPLETE!")
            print("🔒 Your trading system is now protected against hackers")
            print("🚨 Security monitoring is active")
            
            # Clear sensitive variables from memory
            api_key = "CLEARED"
            api_secret = "CLEARED"
            master_password = "CLEARED"
            
            print("\n🚀 Starting secure trading system...")
            
            # Set environment flag
            os.environ['SECURITY_ENABLED'] = 'TRUE'
            
            # Launch the main trading system
            try:
                subprocess.run([sys.executable, "quantum Dogi Trade V2.0.py"], check=True)
            except subprocess.CalledProcessError as e:
                print(f"❌ Trading system error: {e}")
            except KeyboardInterrupt:
                print("\n🛑 Trading system stopped by user")
        else:
            print("❌ Security setup failed")
            sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Secure launcher stopped")
    except Exception as e:
        print(f"❌ Launcher error: {e}")
    finally:
        print("🔒 Secure launcher terminated")
