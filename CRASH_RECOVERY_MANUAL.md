# 🔄 Quantum Trading System - Crash Recovery Manual

## 🚨 What is Crash Recovery?

The Crash Recovery System protects your trades from:
- **Power failures** ⚡
- **PC restarts/updates** 🔄
- **Internet disconnections** 🌐
- **System crashes** 💥
- **Accidental program closure** ❌

## 🛡️ How It Works

### 1. **Automatic State Saving**
- Every time you enter a trade, the system saves:
  - Entry price, Take Profit, Stop Loss
  - Trade direction (Buy Long/Sell Short)
  - Position size and leverage
  - Entry time and reason
  - Current consecutive losses

### 2. **Recovery Files Created**
- `recovery_btc.json` - Bitcoin trade state
- `recovery_eth.json` - Ethereum trade state  
- `recovery_bnb.json` - BNB trade state
- `master_recovery_log.json` - System event log

### 3. **Automatic Recovery on Startup**
- System checks for recovery files when starting
- Restores your trade state automatically
- Continues monitoring TP/SL levels
- Shows current PnL and trade status

## 🔍 How to Check for Recovery

### **Method 1: Run Recovery Checker**
```bash
python recovery_checker.py
```

**This will show:**
- ✅ Any active trades that need recovery
- 📊 Current PnL of recovered trades
- ⚠️ If TP/SL should have been hit
- 🕐 How long ago the crash occurred

### **Method 2: Manual File Check**
Look for files starting with `recovery_` in your trading folder:
- If files exist = Possible crash detected
- If no files = System shut down cleanly

## 🚀 How to Recover After Crash

### **Step 1: Check Recovery Status**
```bash
python recovery_checker.py
```

### **Step 2: Start Your Trading System**
```bash
python "quantum Dogi Trade V2.0.py"
```

**The system will automatically:**
1. 🔍 Detect recovery files
2. 📊 Show recovery information
3. ✅ Restore your trade state
4. 🎯 Continue monitoring TP/SL
5. 📈 Resume normal operation

### **Step 3: Verify Recovery**
Check the startup messages for:
```
🔄 INITIALIZING CRASH RECOVERY SYSTEM...
[RECOVERY] 🚨 TRADE RECOVERY DETECTED for BTC
[RECOVERY] Entry Price: 45000.00
[RECOVERY] Signal: Buy Long
[RECOVERY] Take Profit: 46350.00
[RECOVERY] Stop Loss: 44100.00
[RECOVERY] ✅ Continuing to monitor trade...
✅ CRASH RECOVERY SYSTEM INITIALIZED
```

## 📊 Recovery Information Display

When recovery is detected, you'll see:

```
🔄 CRASH RECOVERY SYSTEM - BTC
============================================================
[RECOVERY] 🚨 TRADE RECOVERY DETECTED for BTC
[RECOVERY] Entry Price: 45000.00
[RECOVERY] Signal: Buy Long
[RECOVERY] Take Profit: 46350.00
[RECOVERY] Stop Loss: 44100.00
[RECOVERY] Entry Time: 2024-01-15 14:30:25
[RECOVERY] Current Price: 45750.00
[RECOVERY] Current PnL: 1.67%

[RECOVERY] 🤔 RECOVERY OPTIONS:
1. Continue monitoring the trade
2. Close the trade immediately  
3. Ignore and start fresh

[RECOVERY] ✅ Continuing to monitor trade...
```

## ⚠️ Important Recovery Scenarios

### **Scenario 1: TP/SL Should Have Hit**
```
[RECOVERY] ⚠️ TAKE PROFIT SHOULD HAVE HIT! 
Current: 46500, TP: 46350
```
**Action:** System will close the trade immediately and log the profit

### **Scenario 2: Trade Still Active**
```
[RECOVERY] Current Price: 45200.00
[RECOVERY] Current PnL: 0.44%
[RECOVERY] ✅ Continuing to monitor trade...
```
**Action:** System resumes normal TP/SL monitoring

### **Scenario 3: Old Recovery Data**
```
[RECOVERY] Recovery data for BTC is too old (25.5 hours), ignoring
```
**Action:** System ignores old data and starts fresh

## 🧹 Cleanup and Maintenance

### **Automatic Cleanup**
- Recovery files are deleted when trades complete normally
- Old files (>24 hours) are automatically ignored
- Master log keeps last 100 recovery events

### **Manual Cleanup**
```bash
python recovery_checker.py
# Choose 'y' when asked about cleanup
```

### **Emergency Manual Cleanup**
If you need to manually clear all recovery data:
```bash
# Delete all recovery files
del recovery_*.json
del master_recovery_log.json
```

## 🔧 Technical Details

### **Recovery File Structure**
```json
{
  "timestamp": "2024-01-15T14:30:25.123456",
  "asset": "BTC",
  "in_trade": true,
  "signal": "Buy Long",
  "entry_price": 45000.00,
  "tp_close_price": 46350.00,
  "sl_close_price": 44100.00,
  "leverage": 1,
  "position_size": 0.001,
  "entry_time": "2024-01-15T14:30:25",
  "consecutive_losses": 0,
  "recovery_version": "1.0"
}
```

### **When Recovery Files Are Created**
- ✅ Immediately after trade entry
- ✅ Updated during trade monitoring
- ✅ Deleted when trade closes normally

### **When Recovery Triggers**
- 🔄 System startup
- 🔄 Program restart
- 🔄 After crash/power failure

## 🎯 Best Practices

### **1. Regular Checks**
- Run `recovery_checker.py` daily
- Check for recovery files before trading
- Monitor master recovery log

### **2. Safe Shutdown**
- Always close trades properly
- Let system complete exit logging
- Avoid force-closing the program

### **3. Backup Strategy**
- Keep recovery files backed up
- Save master recovery log
- Document any manual interventions

## 🆘 Troubleshooting

### **Problem: Recovery Not Working**
**Solution:**
1. Check if recovery files exist
2. Verify file permissions
3. Check timestamp validity
4. Run recovery_checker.py for diagnosis

### **Problem: False Recovery Alerts**
**Solution:**
1. Clean up old recovery files
2. Check system clock accuracy
3. Verify trade completion logging

### **Problem: Missing Trade Data**
**Solution:**
1. Check CSV trade logs
2. Verify exchange positions
3. Review master recovery log
4. Contact support if needed

## 📞 Emergency Contacts

If recovery fails or you need help:
1. 📊 Check your exchange account directly
2. 📋 Review CSV trade logs
3. 🔍 Run full system diagnostics
4. 💾 Backup all recovery files before changes

---

**🔄 Your trades are now protected against crashes, power failures, and system restarts!**
