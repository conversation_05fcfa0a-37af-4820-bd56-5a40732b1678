#!/usr/bin/env python3
"""
Performance Optimization Engine for Quantum Trading System
Implements caching, parallel processing, and performance monitoring
"""

import time
import threading
import functools
import concurrent.futures
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json
import psutil
import gc
import numpy as np

class PerformanceMonitor:
    """Monitor system performance and trading metrics"""
    
    def __init__(self):
        self.metrics = {
            'api_calls': defaultdict(int),
            'execution_times': defaultdict(list),
            'memory_usage': deque(maxlen=100),
            'cpu_usage': deque(maxlen=100),
            'trade_latency': deque(maxlen=50),
            'error_counts': defaultdict(int)
        }
        self.start_time = time.time()
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_system, daemon=True)
        self.monitor_thread.start()
    
    def _monitor_system(self):
        """Background system monitoring"""
        while self.monitoring:
            try:
                # Monitor memory and CPU
                memory_percent = psutil.virtual_memory().percent
                cpu_percent = psutil.cpu_percent()
                
                self.metrics['memory_usage'].append({
                    'timestamp': datetime.now().isoformat(),
                    'memory_percent': memory_percent,
                    'memory_mb': psutil.virtual_memory().used / 1024 / 1024
                })
                
                self.metrics['cpu_usage'].append({
                    'timestamp': datetime.now().isoformat(),
                    'cpu_percent': cpu_percent
                })
                
                # Trigger garbage collection if memory usage is high
                if memory_percent > 80:
                    gc.collect()
                    print(f"[PERFORMANCE] High memory usage ({memory_percent:.1f}%), triggered GC")
                
                time.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                print(f"[PERFORMANCE ERROR] Monitoring error: {e}")
                time.sleep(10)
    
    def record_api_call(self, endpoint):
        """Record API call for rate limiting analysis"""
        self.metrics['api_calls'][endpoint] += 1
    
    def record_execution_time(self, function_name, execution_time):
        """Record function execution time"""
        self.metrics['execution_times'][function_name].append({
            'timestamp': datetime.now().isoformat(),
            'duration': execution_time
        })
        
        # Keep only last 100 measurements per function
        if len(self.metrics['execution_times'][function_name]) > 100:
            self.metrics['execution_times'][function_name] = \
                self.metrics['execution_times'][function_name][-100:]
    
    def record_trade_latency(self, latency_ms):
        """Record trade execution latency"""
        self.metrics['trade_latency'].append({
            'timestamp': datetime.now().isoformat(),
            'latency_ms': latency_ms
        })
    
    def record_error(self, error_type):
        """Record error occurrence"""
        self.metrics['error_counts'][error_type] += 1
    
    def get_performance_report(self):
        """Generate comprehensive performance report"""
        uptime = time.time() - self.start_time
        
        report = {
            'system_uptime_hours': uptime / 3600,
            'total_api_calls': sum(self.metrics['api_calls'].values()),
            'api_calls_by_endpoint': dict(self.metrics['api_calls']),
            'average_memory_usage': np.mean([m['memory_percent'] for m in self.metrics['memory_usage']]) if self.metrics['memory_usage'] else 0,
            'average_cpu_usage': np.mean([c['cpu_percent'] for c in self.metrics['cpu_usage']]) if self.metrics['cpu_usage'] else 0,
            'average_trade_latency_ms': np.mean([t['latency_ms'] for t in self.metrics['trade_latency']]) if self.metrics['trade_latency'] else 0,
            'total_errors': sum(self.metrics['error_counts'].values()),
            'errors_by_type': dict(self.metrics['error_counts'])
        }
        
        # Function performance analysis
        function_performance = {}
        for func_name, times in self.metrics['execution_times'].items():
            if times:
                durations = [t['duration'] for t in times]
                function_performance[func_name] = {
                    'average_ms': np.mean(durations) * 1000,
                    'max_ms': np.max(durations) * 1000,
                    'min_ms': np.min(durations) * 1000,
                    'call_count': len(durations)
                }
        
        report['function_performance'] = function_performance
        return report

class SmartCache:
    """Intelligent caching system for trading data"""
    
    def __init__(self, max_size=1000, ttl_seconds=60):
        self.cache = {}
        self.access_times = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.lock = threading.RLock()
    
    def get(self, key):
        """Get cached value if valid"""
        with self.lock:
            if key in self.cache:
                # Check TTL
                if time.time() - self.access_times[key] < self.ttl_seconds:
                    self.access_times[key] = time.time()  # Update access time
                    return self.cache[key]
                else:
                    # Expired, remove from cache
                    del self.cache[key]
                    del self.access_times[key]
            return None
    
    def set(self, key, value):
        """Set cached value with automatic cleanup"""
        with self.lock:
            # Clean up if cache is full
            if len(self.cache) >= self.max_size:
                self._cleanup_old_entries()
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def _cleanup_old_entries(self):
        """Remove oldest entries when cache is full"""
        # Remove expired entries first
        current_time = time.time()
        expired_keys = [
            key for key, access_time in self.access_times.items()
            if current_time - access_time >= self.ttl_seconds
        ]
        
        for key in expired_keys:
            del self.cache[key]
            del self.access_times[key]
        
        # If still full, remove oldest entries
        if len(self.cache) >= self.max_size:
            # Sort by access time and remove oldest 20%
            sorted_keys = sorted(self.access_times.items(), key=lambda x: x[1])
            keys_to_remove = [key for key, _ in sorted_keys[:len(sorted_keys) // 5]]
            
            for key in keys_to_remove:
                del self.cache[key]
                del self.access_times[key]
    
    def clear(self):
        """Clear all cached data"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def get_stats(self):
        """Get cache statistics"""
        with self.lock:
            current_time = time.time()
            valid_entries = sum(
                1 for access_time in self.access_times.values()
                if current_time - access_time < self.ttl_seconds
            )
            
            return {
                'total_entries': len(self.cache),
                'valid_entries': valid_entries,
                'expired_entries': len(self.cache) - valid_entries,
                'cache_size_mb': len(str(self.cache)) / 1024 / 1024,
                'hit_rate': getattr(self, '_hit_count', 0) / max(getattr(self, '_total_requests', 1), 1)
            }

def performance_timer(func):
    """Decorator to measure function execution time"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Record performance if monitor exists
            if hasattr(wrapper, '_performance_monitor'):
                wrapper._performance_monitor.record_execution_time(func.__name__, execution_time)
            
            # Log slow functions
            if execution_time > 1.0:  # Log functions taking more than 1 second
                print(f"[PERFORMANCE WARNING] {func.__name__} took {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            if hasattr(wrapper, '_performance_monitor'):
                wrapper._performance_monitor.record_error(f"{func.__name__}_error")
            raise e
    
    return wrapper

def cached_api_call(cache_instance, ttl_seconds=30):
    """Decorator for caching API calls"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # Try to get from cache first
            cached_result = cache_instance.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Call function and cache result
            result = func(*args, **kwargs)
            cache_instance.set(cache_key, result)
            
            return result
        return wrapper
    return decorator

class ParallelProcessor:
    """Parallel processing for multiple asset analysis"""
    
    def __init__(self, max_workers=4):
        self.max_workers = max_workers
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers)
    
    def process_assets_parallel(self, assets, processing_function, *args, **kwargs):
        """Process multiple assets in parallel"""
        futures = {}
        
        for asset in assets:
            future = self.executor.submit(processing_function, asset, *args, **kwargs)
            futures[asset] = future
        
        results = {}
        for asset, future in futures.items():
            try:
                results[asset] = future.result(timeout=30)  # 30 second timeout
            except Exception as e:
                print(f"[PARALLEL ERROR] Error processing {asset}: {e}")
                results[asset] = None
        
        return results
    
    def shutdown(self):
        """Shutdown the executor"""
        self.executor.shutdown(wait=True)

# Global instances
performance_monitor = PerformanceMonitor()
smart_cache = SmartCache(max_size=500, ttl_seconds=30)
parallel_processor = ParallelProcessor(max_workers=3)

# Attach monitor to performance timer
performance_timer._performance_monitor = performance_monitor

def optimize_system():
    """Run system optimization"""
    print("🚀 RUNNING SYSTEM OPTIMIZATION...")
    
    # Clear old cache entries
    smart_cache._cleanup_old_entries()
    
    # Force garbage collection
    gc.collect()
    
    # Get performance report
    report = performance_monitor.get_performance_report()
    
    print(f"📊 PERFORMANCE REPORT:")
    print(f"   Uptime: {report['system_uptime_hours']:.1f} hours")
    print(f"   Memory Usage: {report['average_memory_usage']:.1f}%")
    print(f"   CPU Usage: {report['average_cpu_usage']:.1f}%")
    print(f"   API Calls: {report['total_api_calls']}")
    print(f"   Trade Latency: {report['average_trade_latency_ms']:.1f}ms")
    print(f"   Total Errors: {report['total_errors']}")
    
    # Cache statistics
    cache_stats = smart_cache.get_stats()
    print(f"💾 CACHE STATISTICS:")
    print(f"   Entries: {cache_stats['valid_entries']}/{cache_stats['total_entries']}")
    print(f"   Size: {cache_stats['cache_size_mb']:.2f}MB")
    print(f"   Hit Rate: {cache_stats['hit_rate']:.2%}")
    
    return report

if __name__ == "__main__":
    # Test the performance system
    optimize_system()
