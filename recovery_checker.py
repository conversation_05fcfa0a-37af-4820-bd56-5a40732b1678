#!/usr/bin/env python3
"""
Crash Recovery Checker for Quantum Trading System
Run this script to check for and recover from system crashes, power failures, or restarts
"""

import json
import os
from datetime import datetime, timedelta
import requests

def check_recovery_files():
    """Check for any recovery files and display their status"""
    print("=" * 70)
    print("🔄 QUANTUM TRADING SYSTEM - CRASH RECOVERY CHECKER")
    print("=" * 70)
    print(f"📅 Check Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    recovery_files = []
    
    # Find all recovery files
    for file in os.listdir('.'):
        if file.startswith('recovery_') and file.endswith('.json'):
            recovery_files.append(file)
    
    if not recovery_files:
        print("✅ No recovery files found - System was shut down cleanly")
        return
    
    print(f"⚠️  Found {len(recovery_files)} recovery file(s) - Possible crash detected!")
    print("\n📋 RECOVERY FILE ANALYSIS:")
    print("-" * 70)
    
    for recovery_file in recovery_files:
        try:
            with open(recovery_file, 'r') as f:
                recovery_data = json.load(f)
            
            asset = recovery_data.get('asset', 'Unknown')
            timestamp = recovery_data.get('timestamp', 'Unknown')
            in_trade = recovery_data.get('in_trade', False)
            signal = recovery_data.get('signal', 'Unknown')
            entry_price = recovery_data.get('entry_price')
            tp_price = recovery_data.get('tp_close_price')
            sl_price = recovery_data.get('sl_close_price')
            
            print(f"\n🔍 Recovery File: {recovery_file}")
            print(f"   Asset: {asset}")
            print(f"   Last Update: {timestamp}")
            print(f"   In Trade: {'🟢 YES' if in_trade else '🔴 NO'}")
            
            if in_trade:
                print(f"   Signal: {signal}")
                print(f"   Entry Price: {entry_price}")
                print(f"   Take Profit: {tp_price}")
                print(f"   Stop Loss: {sl_price}")
                
                # Check how long ago this was
                try:
                    recovery_time = datetime.fromisoformat(timestamp)
                    time_diff = datetime.now() - recovery_time
                    hours_ago = time_diff.total_seconds() / 3600
                    
                    print(f"   Time Since Last Update: {hours_ago:.1f} hours ago")
                    
                    if hours_ago > 24:
                        print(f"   ⚠️  WARNING: Recovery data is over 24 hours old!")
                    elif hours_ago > 1:
                        print(f"   ⚠️  CAUTION: Recovery data is {hours_ago:.1f} hours old")
                    else:
                        print(f"   ✅ Recent recovery data")
                        
                    # Try to get current price and assess situation
                    try:
                        current_price = get_current_price(asset)
                        if current_price and entry_price:
                            print(f"   Current Price: {current_price}")
                            
                            # Calculate current PnL
                            if signal == "Buy Long":
                                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                            elif signal == "Sell Short":
                                pnl_pct = ((entry_price - current_price) / entry_price) * 100
                            else:
                                pnl_pct = 0
                            
                            print(f"   Current PnL: {pnl_pct:.2f}%")
                            
                            # Check if TP/SL should have triggered
                            if tp_price and sl_price:
                                if signal == "Buy Long":
                                    if current_price >= tp_price:
                                        print(f"   🎯 TAKE PROFIT HIT! (Target: {tp_price})")
                                    elif current_price <= sl_price:
                                        print(f"   🛑 STOP LOSS HIT! (Target: {sl_price})")
                                    else:
                                        print(f"   📊 Trade still active")
                                elif signal == "Sell Short":
                                    if current_price <= tp_price:
                                        print(f"   🎯 TAKE PROFIT HIT! (Target: {tp_price})")
                                    elif current_price >= sl_price:
                                        print(f"   🛑 STOP LOSS HIT! (Target: {sl_price})")
                                    else:
                                        print(f"   📊 Trade still active")
                    except Exception as e:
                        print(f"   ❌ Could not fetch current price: {e}")
                        
                except Exception as e:
                    print(f"   ❌ Could not parse timestamp: {e}")
            
        except Exception as e:
            print(f"❌ Error reading {recovery_file}: {e}")
    
    print("\n" + "=" * 70)
    print("💡 RECOVERY RECOMMENDATIONS:")
    print("=" * 70)
    print("1. 🚀 Start your trading system to automatically recover")
    print("2. 📊 Check your exchange account for actual positions")
    print("3. 🔍 Review the recovery data above for trade status")
    print("4. ⚠️  If data is old (>24h), consider manual cleanup")
    print("=" * 70)

def get_current_price(asset):
    """Get current price from Binance API"""
    try:
        symbol = asset + "USDT"
        url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            return float(data['price'])
        else:
            return None
    except Exception as e:
        print(f"Error fetching price for {asset}: {e}")
        return None

def cleanup_old_recovery_files():
    """Clean up recovery files older than 24 hours"""
    print("\n🧹 CLEANING UP OLD RECOVERY FILES...")
    
    cleaned_count = 0
    for file in os.listdir('.'):
        if file.startswith('recovery_') and file.endswith('.json'):
            try:
                with open(file, 'r') as f:
                    recovery_data = json.load(f)
                
                timestamp = recovery_data.get('timestamp')
                if timestamp:
                    recovery_time = datetime.fromisoformat(timestamp)
                    time_diff = datetime.now() - recovery_time
                    
                    if time_diff.total_seconds() > 86400:  # 24 hours
                        os.remove(file)
                        print(f"🗑️  Removed old recovery file: {file}")
                        cleaned_count += 1
                        
            except Exception as e:
                print(f"❌ Error processing {file}: {e}")
    
    if cleaned_count > 0:
        print(f"✅ Cleaned up {cleaned_count} old recovery file(s)")
    else:
        print("✅ No old recovery files to clean up")

def show_master_recovery_log():
    """Show the master recovery log"""
    master_log = "master_recovery_log.json"
    
    if not os.path.exists(master_log):
        print("\n📋 No master recovery log found")
        return
    
    try:
        with open(master_log, 'r') as f:
            log_data = json.load(f)
        
        events = log_data.get('recovery_events', [])
        
        if not events:
            print("\n📋 Master recovery log is empty")
            return
        
        print(f"\n📋 MASTER RECOVERY LOG (Last {min(10, len(events))} events):")
        print("-" * 70)
        
        # Show last 10 events
        for event in events[-10:]:
            timestamp = event.get('timestamp', 'Unknown')
            event_type = event.get('event', 'Unknown')
            files_found = event.get('recovery_files_found', [])
            status = event.get('status', 'Unknown')
            
            print(f"📅 {timestamp}")
            print(f"   Event: {event_type}")
            print(f"   Files Found: {len(files_found)}")
            print(f"   Status: {status}")
            if files_found:
                for file in files_found:
                    print(f"     - {file}")
            print()
            
    except Exception as e:
        print(f"❌ Error reading master recovery log: {e}")

if __name__ == "__main__":
    # Run the recovery check
    check_recovery_files()
    
    # Show master log
    show_master_recovery_log()
    
    # Ask about cleanup
    print(f"\n🧹 Clean up old recovery files? (y/n): ", end="")
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes']:
            cleanup_old_recovery_files()
    except:
        pass  # Handle if running in non-interactive environment
    
    print(f"\n🔄 Run this script anytime to check for crash recovery!")
    print(f"📁 Recovery files location: current directory")
    print(f"🚀 To recover: Run your main trading system - it will auto-recover")
