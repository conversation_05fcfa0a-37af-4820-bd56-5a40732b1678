#!/usr/bin/env python3
"""
Payment Gateway Integration Setup Script
Installs required dependencies for multi-customer trading system
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Install all required packages for Payment Gateway Integration"""
    print("🚀 Installing Payment Gateway Integration Dependencies...")
    print("=" * 60)
    
    # Required packages for Payment Gateway Integration
    packages = [
        "flask",
        "flask-cors", 
        "requests"
    ]
    
    success_count = 0
    total_packages = len(packages)
    
    for package in packages:
        print(f"\n📦 Installing {package}...")
        if install_package(package):
            success_count += 1
        else:
            print(f"⚠️ Failed to install {package}")
    
    print("\n" + "=" * 60)
    print(f"📊 Installation Summary:")
    print(f"✅ Successfully installed: {success_count}/{total_packages} packages")
    
    if success_count == total_packages:
        print("🎉 All dependencies installed successfully!")
        print("\n🔗 Payment Gateway Integration is now ready!")
        print("\n📋 Next Steps:")
        print("1. Run your Trading System: python 'quantum Dogi Trade V2.0.py'")
        print("2. Trading System API will be available at: http://localhost:8080")
        print("3. Dashboard will be available at: http://localhost:9089")
        print("4. Your Payment Gateway can now communicate with the Trading System!")
    else:
        print("⚠️ Some packages failed to install. Please install them manually:")
        print("pip install flask flask-cors requests")
    
    print("\n💰 Multi-Customer Trading System Ready for Integration!")

if __name__ == "__main__":
    main()
