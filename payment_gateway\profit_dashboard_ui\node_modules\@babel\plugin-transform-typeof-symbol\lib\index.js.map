{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "<PERSON><PERSON>", "scope", "getBinding", "rename", "UnaryExpression", "path", "node", "parent", "operator", "parentPath", "isBinaryExpression", "t", "EQUALITY_BINARY_OPERATORS", "includes", "opposite", "getOpposite", "isStringLiteral", "value", "isUnderHelper", "findParent", "isFunctionDeclaration", "_path$get", "get", "helper", "addHelper", "isVariableDeclarator", "id", "call", "callExpression", "argument", "arg", "isIdentifier", "hasBinding", "unary", "unaryExpression", "cloneNode", "replaceWith", "conditionalExpression", "binaryExpression", "stringLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-typeof-symbol\",\n\n    visitor: {\n      Scope({ scope }) {\n        if (!scope.getBinding(\"Symbol\")) {\n          return;\n        }\n\n        scope.rename(\"Symbol\");\n      },\n\n      UnaryExpression(path) {\n        const { node, parent } = path;\n        if (node.operator !== \"typeof\") return;\n\n        if (\n          path.parentPath.isBinaryExpression() &&\n          t.EQUALITY_BINARY_OPERATORS.includes(\n            (parent as t.BinaryExpression).operator,\n          )\n        ) {\n          // optimise `typeof foo === \"string\"` since we can determine that they'll never\n          // need to handle symbols\n          const opposite = path.getOpposite();\n          if (\n            opposite.isStringLiteral() &&\n            opposite.node.value !== \"symbol\" &&\n            opposite.node.value !== \"object\"\n          ) {\n            return;\n          }\n        }\n\n        let isUnderHelper = path.findParent(path => {\n          if (path.isFunctionDeclaration()) {\n            return (\n              path.get(\"body.directives.0\")?.node.value.value ===\n              \"@babel/helpers - typeof\"\n            );\n          }\n        });\n\n        if (isUnderHelper) return;\n\n        const helper = this.addHelper(\"typeof\");\n\n        // This is needed for backward compatibility with\n        // @babel/helpers <= 7.8.3.\n        if (!process.env.BABEL_8_BREAKING) {\n          isUnderHelper = path.findParent(path => {\n            return (\n              (path.isVariableDeclarator() && path.node.id === helper) ||\n              (path.isFunctionDeclaration() &&\n                path.node.id &&\n                path.node.id.name === helper.name)\n            );\n          });\n\n          if (isUnderHelper) {\n            return;\n          }\n        }\n\n        const call = t.callExpression(helper, [node.argument]);\n        const arg = path.get(\"argument\");\n        if (arg.isIdentifier() && !path.scope.hasBinding(arg.node.name, true)) {\n          const unary = t.unaryExpression(\"typeof\", t.cloneNode(node.argument));\n          path.replaceWith(\n            t.conditionalExpression(\n              t.binaryExpression(\"===\", unary, t.stringLiteral(\"undefined\")),\n              t.stringLiteral(\"undefined\"),\n              call,\n            ),\n          );\n        } else {\n          path.replaceWith(call);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,yBAAyB;IAE/BC,OAAO,EAAE;MACPC,KAAKA,CAAC;QAAEC;MAAM,CAAC,EAAE;QACf,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;UAC/B;QACF;QAEAD,KAAK,CAACE,MAAM,CAAC,QAAQ,CAAC;MACxB,CAAC;MAEDC,eAAeA,CAACC,IAAI,EAAE;QACpB,MAAM;UAAEC,IAAI;UAAEC;QAAO,CAAC,GAAGF,IAAI;QAC7B,IAAIC,IAAI,CAACE,QAAQ,KAAK,QAAQ,EAAE;QAEhC,IACEH,IAAI,CAACI,UAAU,CAACC,kBAAkB,CAAC,CAAC,IACpCC,WAAC,CAACC,yBAAyB,CAACC,QAAQ,CACjCN,MAAM,CAAwBC,QACjC,CAAC,EACD;UAGA,MAAMM,QAAQ,GAAGT,IAAI,CAACU,WAAW,CAAC,CAAC;UACnC,IACED,QAAQ,CAACE,eAAe,CAAC,CAAC,IAC1BF,QAAQ,CAACR,IAAI,CAACW,KAAK,KAAK,QAAQ,IAChCH,QAAQ,CAACR,IAAI,CAACW,KAAK,KAAK,QAAQ,EAChC;YACA;UACF;QACF;QAEA,IAAIC,aAAa,GAAGb,IAAI,CAACc,UAAU,CAACd,IAAI,IAAI;UAC1C,IAAIA,IAAI,CAACe,qBAAqB,CAAC,CAAC,EAAE;YAAA,IAAAC,SAAA;YAChC,OACE,EAAAA,SAAA,GAAAhB,IAAI,CAACiB,GAAG,CAAC,mBAAmB,CAAC,qBAA7BD,SAAA,CAA+Bf,IAAI,CAACW,KAAK,CAACA,KAAK,MAC/C,yBAAyB;UAE7B;QACF,CAAC,CAAC;QAEF,IAAIC,aAAa,EAAE;QAEnB,MAAMK,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,QAAQ,CAAC;QAIJ;UACjCN,aAAa,GAAGb,IAAI,CAACc,UAAU,CAACd,IAAI,IAAI;YACtC,OACGA,IAAI,CAACoB,oBAAoB,CAAC,CAAC,IAAIpB,IAAI,CAACC,IAAI,CAACoB,EAAE,KAAKH,MAAM,IACtDlB,IAAI,CAACe,qBAAqB,CAAC,CAAC,IAC3Bf,IAAI,CAACC,IAAI,CAACoB,EAAE,IACZrB,IAAI,CAACC,IAAI,CAACoB,EAAE,CAAC5B,IAAI,KAAKyB,MAAM,CAACzB,IAAK;UAExC,CAAC,CAAC;UAEF,IAAIoB,aAAa,EAAE;YACjB;UACF;QACF;QAEA,MAAMS,IAAI,GAAGhB,WAAC,CAACiB,cAAc,CAACL,MAAM,EAAE,CAACjB,IAAI,CAACuB,QAAQ,CAAC,CAAC;QACtD,MAAMC,GAAG,GAAGzB,IAAI,CAACiB,GAAG,CAAC,UAAU,CAAC;QAChC,IAAIQ,GAAG,CAACC,YAAY,CAAC,CAAC,IAAI,CAAC1B,IAAI,CAACJ,KAAK,CAAC+B,UAAU,CAACF,GAAG,CAACxB,IAAI,CAACR,IAAI,EAAE,IAAI,CAAC,EAAE;UACrE,MAAMmC,KAAK,GAAGtB,WAAC,CAACuB,eAAe,CAAC,QAAQ,EAAEvB,WAAC,CAACwB,SAAS,CAAC7B,IAAI,CAACuB,QAAQ,CAAC,CAAC;UACrExB,IAAI,CAAC+B,WAAW,CACdzB,WAAC,CAAC0B,qBAAqB,CACrB1B,WAAC,CAAC2B,gBAAgB,CAAC,KAAK,EAAEL,KAAK,EAAEtB,WAAC,CAAC4B,aAAa,CAAC,WAAW,CAAC,CAAC,EAC9D5B,WAAC,CAAC4B,aAAa,CAAC,WAAW,CAAC,EAC5BZ,IACF,CACF,CAAC;QACH,CAAC,MAAM;UACLtB,IAAI,CAAC+B,WAAW,CAACT,IAAI,CAAC;QACxB;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}