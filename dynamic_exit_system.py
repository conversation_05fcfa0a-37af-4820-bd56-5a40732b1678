#!/usr/bin/env python3
"""
Dynamic Exit System for Maximum Profit Capture
Implements trend-following exits that override TP/SL for maximum gains
"""

# pylint: disable=import-error
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from enum import Enum

# Try to import talib, use fallback if not available
try:
    import talib  # type: ignore
    TALIB_AVAILABLE = True
    print("✅ TA-Lib loaded successfully")
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-Lib not available, using fallback calculations")

    # Create fallback functions
    class talib:  # type: ignore
        @staticmethod
        def SMA(data, timeperiod):
            """Simple Moving Average fallback"""
            if len(data) < timeperiod:
                return np.full(len(data), np.nan)
            result = np.full(len(data), np.nan)
            for i in range(timeperiod-1, len(data)):
                result[i] = np.mean(data[i-timeperiod+1:i+1])
            return result

        @staticmethod
        def RSI(data, timeperiod=14):
            """RSI fallback calculation"""
            if len(data) < timeperiod + 1:
                return np.full(len(data), np.nan)

            deltas = np.diff(data)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gains = np.full(len(data), np.nan)
            avg_losses = np.full(len(data), np.nan)

            # Initial averages
            avg_gains[timeperiod] = np.mean(gains[:timeperiod])
            avg_losses[timeperiod] = np.mean(losses[:timeperiod])

            # Smoothed averages
            for i in range(timeperiod + 1, len(data)):
                avg_gains[i] = (avg_gains[i-1] * (timeperiod - 1) + gains[i-1]) / timeperiod
                avg_losses[i] = (avg_losses[i-1] * (timeperiod - 1) + losses[i-1]) / timeperiod

            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))
            return rsi

        @staticmethod
        def ADX(high, low, close, timeperiod=14):
            """ADX fallback calculation"""
            if len(close) < timeperiod * 2:
                return np.full(len(close), np.nan)

            # Simplified ADX calculation
            tr_list = []
            for i in range(1, len(close)):
                tr = max(
                    high[i] - low[i],
                    abs(high[i] - close[i-1]),
                    abs(low[i] - close[i-1])
                )
                tr_list.append(tr)

            # Simple trend strength approximation
            price_changes = np.diff(close)
            volatility = np.std(price_changes[-timeperiod:]) if len(price_changes) >= timeperiod else 1
            trend_strength = abs(np.mean(price_changes[-timeperiod:])) / volatility if volatility > 0 else 0

            result = np.full(len(close), np.nan)
            result[-1] = min(trend_strength * 50, 100)  # Scale to 0-100
            return result

        @staticmethod
        def ATR(high, low, close, timeperiod=14):
            """ATR fallback calculation"""
            if len(close) < timeperiod + 1:
                return np.full(len(close), np.nan)

            tr_list = []
            for i in range(1, len(close)):
                tr = max(
                    high[i] - low[i],
                    abs(high[i] - close[i-1]),
                    abs(low[i] - close[i-1])
                )
                tr_list.append(tr)

            result = np.full(len(close), np.nan)
            if len(tr_list) >= timeperiod:
                for i in range(timeperiod-1, len(tr_list)):
                    result[i+1] = np.mean(tr_list[i-timeperiod+1:i+1])

            return result

class TrendState(Enum):
    STRONG_BULLISH = "STRONG_BULLISH"
    WEAK_BULLISH = "WEAK_BULLISH"
    NEUTRAL = "NEUTRAL"
    WEAK_BEARISH = "WEAK_BEARISH"
    STRONG_BEARISH = "STRONG_BEARISH"

class ExitReason(Enum):
    TREND_EXHAUSTION = "TREND_EXHAUSTION"
    MOMENTUM_DIVERGENCE = "MOMENTUM_DIVERGENCE"
    VOLUME_DECLINE = "VOLUME_DECLINE"
    REVERSAL_PATTERN = "REVERSAL_PATTERN"
    TRAILING_STOP = "TRAILING_STOP"
    FIXED_TP_SL = "FIXED_TP_SL"
    TIME_BASED = "TIME_BASED"

class DynamicExitSystem:
    """Advanced exit system for maximum profit capture"""
    
    def __init__(self):
        self.trend_strength_threshold = 0.7
        self.momentum_divergence_periods = 5
        self.volume_decline_threshold = 0.3
        self.trailing_stop_atr_multiplier = 2.0
        self.max_hold_periods = 50  # Maximum candles to hold
        
        # Profit protection settings
        self.min_profit_to_override_tp = 0.015  # 1.5% minimum profit to override TP
        self.profit_protection_threshold = 0.02  # 2% profit triggers protection mode
        
    def calculate_trend_strength(self, prices, volumes=None):
        """Calculate comprehensive trend strength"""
        if len(prices) < 20:
            return 0.5, TrendState.NEUTRAL
        
        close = np.array(prices)
        high = np.array(prices)  # Simplified - in real implementation use OHLC
        low = np.array(prices)
        
        try:
            # 1. Price momentum
            price_change = (close[-1] - close[-10]) / close[-10]
            momentum_score = min(abs(price_change) * 10, 1.0)
            
            # 2. Moving average alignment
            sma_5 = talib.SMA(close, timeperiod=5)
            sma_10 = talib.SMA(close, timeperiod=10)
            sma_20 = talib.SMA(close, timeperiod=20)
            
            ma_alignment = 0
            if not (np.isnan(sma_5[-1]) or np.isnan(sma_10[-1]) or np.isnan(sma_20[-1])):
                if sma_5[-1] > sma_10[-1] > sma_20[-1]:  # Bullish alignment
                    ma_alignment = 1.0
                elif sma_5[-1] < sma_10[-1] < sma_20[-1]:  # Bearish alignment
                    ma_alignment = -1.0
                else:
                    ma_alignment = 0.5
            
            # 3. ADX for trend strength
            adx = talib.ADX(high, low, close, timeperiod=14)
            adx_score = min(adx[-1] / 50, 1.0) if not np.isnan(adx[-1]) else 0.5
            
            # 4. RSI momentum
            rsi = talib.RSI(close, timeperiod=14)
            rsi_momentum = 0.5
            if not np.isnan(rsi[-1]):
                if rsi[-1] > 70:
                    rsi_momentum = 0.8  # Strong bullish
                elif rsi[-1] < 30:
                    rsi_momentum = 0.2  # Strong bearish
                elif rsi[-1] > 50:
                    rsi_momentum = 0.7
                else:
                    rsi_momentum = 0.3
            
            # 5. Volume confirmation (if available)
            volume_score = 0.5
            if volumes and len(volumes) >= 10:
                recent_volume = np.mean(volumes[-5:])
                avg_volume = np.mean(volumes[-20:])
                if recent_volume > avg_volume * 1.2:
                    volume_score = 0.8
                elif recent_volume < avg_volume * 0.8:
                    volume_score = 0.3
            
            # Combine all factors
            trend_strength = (momentum_score * 0.3 + 
                            abs(ma_alignment - 0.5) * 2 * 0.25 + 
                            adx_score * 0.25 + 
                            abs(rsi_momentum - 0.5) * 2 * 0.1 + 
                            abs(volume_score - 0.5) * 2 * 0.1)
            
            # Determine trend direction and state
            if price_change > 0 and ma_alignment > 0.5:
                if trend_strength > 0.8:
                    trend_state = TrendState.STRONG_BULLISH
                else:
                    trend_state = TrendState.WEAK_BULLISH
            elif price_change < 0 and ma_alignment < 0.5:
                if trend_strength > 0.8:
                    trend_state = TrendState.STRONG_BEARISH
                else:
                    trend_state = TrendState.WEAK_BEARISH
            else:
                trend_state = TrendState.NEUTRAL
            
            return trend_strength, trend_state
            
        except Exception as e:
            print(f"[TREND ERROR] {e}")
            return 0.5, TrendState.NEUTRAL
    
    def detect_momentum_divergence(self, prices, rsi_values):
        """Detect momentum divergence for exit signals"""
        if len(prices) < self.momentum_divergence_periods or len(rsi_values) < self.momentum_divergence_periods:
            return False, 0
        
        try:
            recent_prices = prices[-self.momentum_divergence_periods:]
            recent_rsi = rsi_values[-self.momentum_divergence_periods:]
            
            # Check for bearish divergence (price up, RSI down)
            price_trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            rsi_trend = recent_rsi[-1] - recent_rsi[0]
            
            # Bearish divergence: price making higher highs, RSI making lower highs
            if price_trend > 0.01 and rsi_trend < -5:
                return True, -0.8  # Strong bearish divergence
            
            # Bullish divergence: price making lower lows, RSI making higher lows
            elif price_trend < -0.01 and rsi_trend > 5:
                return True, 0.8  # Strong bullish divergence
            
            return False, 0
            
        except Exception as e:
            print(f"[DIVERGENCE ERROR] {e}")
            return False, 0
    
    def calculate_trailing_stop(self, prices, entry_price, position_type, atr_periods=14):
        """Calculate dynamic trailing stop based on ATR"""
        if len(prices) < atr_periods:
            return entry_price
        
        try:
            # Calculate ATR
            high = np.array(prices)
            low = np.array(prices)
            close = np.array(prices)
            
            atr = talib.ATR(high, low, close, timeperiod=atr_periods)
            current_atr = atr[-1] if not np.isnan(atr[-1]) else (max(prices[-10:]) - min(prices[-10:])) / 10
            
            current_price = prices[-1]
            
            if position_type.upper() == "BUY":
                # For long positions, trailing stop moves up
                trailing_stop = current_price - (current_atr * self.trailing_stop_atr_multiplier)
                return max(trailing_stop, entry_price * 0.98)  # Never go below 2% loss
            else:
                # For short positions, trailing stop moves down
                trailing_stop = current_price + (current_atr * self.trailing_stop_atr_multiplier)
                return min(trailing_stop, entry_price * 1.02)  # Never go above 2% loss
                
        except Exception as e:
            print(f"[TRAILING STOP ERROR] {e}")
            return entry_price
    
    def should_override_tp_sl(self, current_price, entry_price, position_type, trend_strength, trend_state):
        """Determine if we should override TP/SL for trend continuation"""
        try:
            if position_type.upper() == "BUY":
                current_profit_pct = (current_price - entry_price) / entry_price
                
                # Override TP if:
                # 1. We have minimum profit
                # 2. Trend is still strong
                # 3. No major reversal signals
                if (current_profit_pct > self.min_profit_to_override_tp and 
                    trend_strength > self.trend_strength_threshold and
                    trend_state in [TrendState.STRONG_BULLISH, TrendState.WEAK_BULLISH]):
                    return True, "BULLISH_TREND_CONTINUATION"
                    
            else:  # SELL position
                current_profit_pct = (entry_price - current_price) / entry_price
                
                if (current_profit_pct > self.min_profit_to_override_tp and 
                    trend_strength > self.trend_strength_threshold and
                    trend_state in [TrendState.STRONG_BEARISH, TrendState.WEAK_BEARISH]):
                    return True, "BEARISH_TREND_CONTINUATION"
            
            return False, "NO_OVERRIDE"
            
        except Exception as e:
            print(f"[OVERRIDE ERROR] {e}")
            return False, "ERROR"
    
    def analyze_exit_conditions(self, prices, volumes, entry_price, entry_time, position_type,
                              current_tp, current_sl, periods_held=0):
        """Comprehensive exit analysis"""
        # entry_time is used for time-based exit logic (future enhancement)
        _ = entry_time  # Suppress unused parameter warning

        if len(prices) < 10:
            return False, ExitReason.FIXED_TP_SL, current_tp if position_type.upper() == "BUY" else current_sl
        
        current_price = prices[-1]
        
        try:
            # 1. Calculate trend strength
            trend_strength, trend_state = self.calculate_trend_strength(prices, volumes)
            
            # 2. Check if we should override TP/SL
            should_override, override_reason = self.should_override_tp_sl(
                current_price, entry_price, position_type, trend_strength, trend_state
            )
            
            # 3. Calculate current profit
            if position_type.upper() == "BUY":
                current_profit_pct = (current_price - entry_price) / entry_price
                in_profit = current_profit_pct > 0
            else:
                current_profit_pct = (entry_price - current_price) / entry_price
                in_profit = current_profit_pct > 0
            
            # 4. Check various exit conditions
            
            # A. Time-based exit (prevent holding too long)
            if periods_held > self.max_hold_periods:
                return True, ExitReason.TIME_BASED, current_price
            
            # B. Trend exhaustion
            if trend_strength < 0.3 and in_profit:
                return True, ExitReason.TREND_EXHAUSTION, current_price
            
            # C. Momentum divergence
            if len(prices) >= 14:
                rsi = talib.RSI(np.array(prices), timeperiod=14)
                if not np.isnan(rsi[-1]):
                    has_divergence, divergence_strength = self.detect_momentum_divergence(prices, rsi)
                    if has_divergence and in_profit:
                        # Check if divergence is against our position
                        if ((position_type.upper() == "BUY" and divergence_strength < -0.5) or
                            (position_type.upper() == "SELL" and divergence_strength > 0.5)):
                            return True, ExitReason.MOMENTUM_DIVERGENCE, current_price
            
            # D. Volume decline (trend weakening)
            if volumes and len(volumes) >= 10:
                recent_volume = np.mean(volumes[-3:])
                avg_volume = np.mean(volumes[-10:])
                if recent_volume < avg_volume * (1 - self.volume_decline_threshold) and in_profit:
                    return True, ExitReason.VOLUME_DECLINE, current_price
            
            # E. Trailing stop (profit protection)
            if in_profit and current_profit_pct > self.profit_protection_threshold:
                trailing_stop = self.calculate_trailing_stop(prices, entry_price, position_type)
                
                if position_type.upper() == "BUY" and current_price <= trailing_stop:
                    return True, ExitReason.TRAILING_STOP, trailing_stop
                elif position_type.upper() == "SELL" and current_price >= trailing_stop:
                    return True, ExitReason.TRAILING_STOP, trailing_stop
            
            # F. Strong reversal pattern
            if len(prices) >= 5:
                recent_prices = prices[-5:]
                if position_type.upper() == "BUY":
                    # Look for bearish reversal (3 consecutive lower highs)
                    if (recent_prices[-1] < recent_prices[-2] < recent_prices[-3] and 
                        current_profit_pct > 0.01):  # At least 1% profit
                        return True, ExitReason.REVERSAL_PATTERN, current_price
                else:
                    # Look for bullish reversal (3 consecutive higher lows)
                    if (recent_prices[-1] > recent_prices[-2] > recent_prices[-3] and 
                        current_profit_pct > 0.01):  # At least 1% profit
                        return True, ExitReason.REVERSAL_PATTERN, current_price
            
            # G. If no dynamic exit conditions met, check if we should override TP/SL
            if should_override:
                print(f"[DYNAMIC EXIT] Overriding TP/SL - {override_reason}")
                print(f"[DYNAMIC EXIT] Trend Strength: {trend_strength:.2f}, State: {trend_state}")
                print(f"[DYNAMIC EXIT] Current Profit: {current_profit_pct:.2%}")
                return False, None, None  # Continue holding
            
            # H. Default to original TP/SL
            if position_type.upper() == "BUY":
                if current_price >= current_tp:
                    return True, ExitReason.FIXED_TP_SL, current_tp
                elif current_price <= current_sl:
                    return True, ExitReason.FIXED_TP_SL, current_sl
            else:
                if current_price <= current_tp:
                    return True, ExitReason.FIXED_TP_SL, current_tp
                elif current_price >= current_sl:
                    return True, ExitReason.FIXED_TP_SL, current_sl
            
            return False, None, None  # Continue holding
            
        except Exception as e:
            print(f"[EXIT ANALYSIS ERROR] {e}")
            return False, ExitReason.FIXED_TP_SL, current_tp if position_type.upper() == "BUY" else current_sl
    
    def get_exit_recommendation(self, prices, volumes, entry_price, entry_time, position_type, 
                              current_tp, current_sl, periods_held=0):
        """Get comprehensive exit recommendation"""
        should_exit, exit_reason, exit_price = self.analyze_exit_conditions(
            prices, volumes, entry_price, entry_time, position_type, 
            current_tp, current_sl, periods_held
        )
        
        current_price = prices[-1] if prices else entry_price
        
        if position_type.upper() == "BUY":
            current_profit_pct = (current_price - entry_price) / entry_price
        else:
            current_profit_pct = (entry_price - current_price) / entry_price
        
        recommendation = {
            'should_exit': should_exit,
            'exit_reason': exit_reason.value if exit_reason else None,
            'recommended_exit_price': exit_price,
            'current_price': current_price,
            'current_profit_pct': current_profit_pct,
            'current_profit_usdt': current_profit_pct * entry_price,
            'periods_held': periods_held,
            'trend_analysis': self.calculate_trend_strength(prices, volumes)
        }
        
        return recommendation

# Global instance
dynamic_exit_system = DynamicExitSystem()

def analyze_dynamic_exit(prices, volumes, entry_price, entry_time, position_type, 
                        current_tp, current_sl, periods_held=0):
    """Main function to analyze dynamic exit conditions"""
    return dynamic_exit_system.get_exit_recommendation(
        prices, volumes, entry_price, entry_time, position_type, 
        current_tp, current_sl, periods_held
    )

if __name__ == "__main__":
    # Test the dynamic exit system
    print("🎯 Dynamic Exit System - Maximum Profit Capture")
    print("=" * 50)
    
    # Simulate price data
    test_prices = [100, 101, 102, 105, 108, 110, 112, 115, 118, 120, 119, 118, 116]
    test_volumes = [1000] * len(test_prices)
    
    recommendation = analyze_dynamic_exit(
        prices=test_prices,
        volumes=test_volumes,
        entry_price=100,
        entry_time=datetime.now(),
        position_type="BUY",
        current_tp=110,  # Original TP
        current_sl=95,   # Original SL
        periods_held=12
    )
    
    print(f"Should Exit: {recommendation['should_exit']}")
    print(f"Exit Reason: {recommendation['exit_reason']}")
    print(f"Current Profit: {recommendation['current_profit_pct']:.2%}")
    print(f"Trend Strength: {recommendation['trend_analysis'][0]:.2f}")
    print(f"Trend State: {recommendation['trend_analysis'][1]}")
