#!/usr/bin/env python3
"""
Advanced Analytics Engine for Quantum Trading System
Implements institutional-grade performance metrics and analysis
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import json
import os
from scipy import stats
from sklearn.metrics import accuracy_score, precision_score, recall_score
import warnings
warnings.filterwarnings('ignore')

class AdvancedAnalytics:
    """Advanced trading analytics and performance metrics"""
    
    def __init__(self, csv_file="trade_log.csv"):
        self.csv_file = csv_file
        self.data = None
        self.load_data()
    
    def load_data(self):
        """Load trading data from CSV"""
        try:
            if os.path.exists(self.csv_file):
                self.data = pd.read_csv(self.csv_file)
                self.data['timestamp'] = pd.to_datetime(self.data['timestamp'])
                print(f"📊 Loaded {len(self.data)} trading records")
            else:
                print(f"⚠️ No trading data found at {self.csv_file}")
                self.data = pd.DataFrame()
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            self.data = pd.DataFrame()
    
    def calculate_sharpe_ratio(self, returns, risk_free_rate=0.02):
        """Calculate Sharpe Ratio"""
        if len(returns) == 0:
            return 0
        
        excess_returns = returns - (risk_free_rate / 252)  # Daily risk-free rate
        if excess_returns.std() == 0:
            return 0
        
        return (excess_returns.mean() / excess_returns.std()) * np.sqrt(252)
    
    def calculate_sortino_ratio(self, returns, risk_free_rate=0.02):
        """Calculate Sortino Ratio (focuses on downside deviation)"""
        if len(returns) == 0:
            return 0
        
        excess_returns = returns - (risk_free_rate / 252)
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return float('inf') if excess_returns.mean() > 0 else 0
        
        return (excess_returns.mean() / downside_returns.std()) * np.sqrt(252)
    
    def calculate_calmar_ratio(self, returns):
        """Calculate Calmar Ratio (Annual Return / Max Drawdown)"""
        if len(returns) == 0:
            return 0
        
        annual_return = returns.mean() * 252
        max_drawdown = self.calculate_max_drawdown(returns)
        
        if max_drawdown == 0:
            return float('inf') if annual_return > 0 else 0
        
        return annual_return / abs(max_drawdown)
    
    def calculate_max_drawdown(self, returns):
        """Calculate Maximum Drawdown"""
        if len(returns) == 0:
            return 0
        
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        return drawdown.min()
    
    def calculate_var(self, returns, confidence_level=0.05):
        """Calculate Value at Risk (VaR)"""
        if len(returns) == 0:
            return 0
        
        return np.percentile(returns, confidence_level * 100)
    
    def calculate_cvar(self, returns, confidence_level=0.05):
        """Calculate Conditional Value at Risk (CVaR)"""
        if len(returns) == 0:
            return 0
        
        var = self.calculate_var(returns, confidence_level)
        return returns[returns <= var].mean()
    
    def analyze_trade_patterns(self):
        """Analyze trading patterns and behaviors"""
        if self.data.empty:
            return {}
        
        exits = self.data[self.data['action'] == 'EXIT'].copy()
        if exits.empty:
            return {}
        
        # Calculate returns
        exits['returns'] = exits['pnl_pct'] / 100
        
        # Basic statistics
        total_trades = len(exits)
        winning_trades = len(exits[exits['pnl_pct'] > 0])
        losing_trades = len(exits[exits['pnl_pct'] < 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        # Return statistics
        avg_return = exits['returns'].mean()
        std_return = exits['returns'].std()
        
        # Risk metrics
        sharpe = self.calculate_sharpe_ratio(exits['returns'])
        sortino = self.calculate_sortino_ratio(exits['returns'])
        calmar = self.calculate_calmar_ratio(exits['returns'])
        max_dd = self.calculate_max_drawdown(exits['returns'])
        var_5 = self.calculate_var(exits['returns'], 0.05)
        cvar_5 = self.calculate_cvar(exits['returns'], 0.05)
        
        # Trade duration analysis
        if 'trade_duration_minutes' in exits.columns:
            avg_duration = exits['trade_duration_minutes'].mean()
            max_duration = exits['trade_duration_minutes'].max()
            min_duration = exits['trade_duration_minutes'].min()
        else:
            avg_duration = max_duration = min_duration = 0
        
        # Consecutive analysis
        consecutive_wins = self.calculate_consecutive_streaks(exits['pnl_pct'] > 0)
        consecutive_losses = self.calculate_consecutive_streaks(exits['pnl_pct'] < 0)
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'avg_return_pct': avg_return * 100,
            'volatility_pct': std_return * 100,
            'sharpe_ratio': sharpe,
            'sortino_ratio': sortino,
            'calmar_ratio': calmar,
            'max_drawdown_pct': max_dd * 100,
            'var_5_pct': var_5 * 100,
            'cvar_5_pct': cvar_5 * 100,
            'avg_duration_minutes': avg_duration,
            'max_duration_minutes': max_duration,
            'min_duration_minutes': min_duration,
            'max_consecutive_wins': consecutive_wins,
            'max_consecutive_losses': consecutive_losses
        }
    
    def calculate_consecutive_streaks(self, boolean_series):
        """Calculate maximum consecutive streaks"""
        if len(boolean_series) == 0:
            return 0
        
        streaks = []
        current_streak = 0
        
        for value in boolean_series:
            if value:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        
        if current_streak > 0:
            streaks.append(current_streak)
        
        return max(streaks) if streaks else 0
    
    def analyze_by_asset(self):
        """Analyze performance by asset"""
        if self.data.empty:
            return {}
        
        exits = self.data[self.data['action'] == 'EXIT'].copy()
        if exits.empty:
            return {}
        
        asset_analysis = {}
        
        for asset in exits['asset'].unique():
            asset_data = exits[exits['asset'] == asset]
            
            if len(asset_data) > 0:
                asset_analysis[asset] = {
                    'total_trades': len(asset_data),
                    'win_rate': len(asset_data[asset_data['pnl_pct'] > 0]) / len(asset_data),
                    'avg_pnl_pct': asset_data['pnl_pct'].mean(),
                    'total_pnl_usdt': asset_data['pnl_usdt'].sum(),
                    'best_trade_pct': asset_data['pnl_pct'].max(),
                    'worst_trade_pct': asset_data['pnl_pct'].min(),
                    'volatility': asset_data['pnl_pct'].std()
                }
        
        return asset_analysis
    
    def analyze_by_timeframe(self):
        """Analyze performance by time periods"""
        if self.data.empty:
            return {}
        
        exits = self.data[self.data['action'] == 'EXIT'].copy()
        if exits.empty:
            return {}
        
        exits['date'] = exits['timestamp'].dt.date
        exits['hour'] = exits['timestamp'].dt.hour
        exits['day_of_week'] = exits['timestamp'].dt.day_name()
        
        timeframe_analysis = {
            'by_hour': {},
            'by_day_of_week': {},
            'by_date': {}
        }
        
        # Hourly analysis
        for hour in range(24):
            hour_data = exits[exits['hour'] == hour]
            if len(hour_data) > 0:
                timeframe_analysis['by_hour'][hour] = {
                    'trades': len(hour_data),
                    'win_rate': len(hour_data[hour_data['pnl_pct'] > 0]) / len(hour_data),
                    'avg_pnl_pct': hour_data['pnl_pct'].mean()
                }
        
        # Day of week analysis
        for day in exits['day_of_week'].unique():
            day_data = exits[exits['day_of_week'] == day]
            timeframe_analysis['by_day_of_week'][day] = {
                'trades': len(day_data),
                'win_rate': len(day_data[day_data['pnl_pct'] > 0]) / len(day_data),
                'avg_pnl_pct': day_data['pnl_pct'].mean()
            }
        
        # Daily analysis (last 30 days)
        recent_dates = exits['date'].unique()[-30:]
        for date in recent_dates:
            date_data = exits[exits['date'] == date]
            timeframe_analysis['by_date'][str(date)] = {
                'trades': len(date_data),
                'win_rate': len(date_data[date_data['pnl_pct'] > 0]) / len(date_data) if len(date_data) > 0 else 0,
                'total_pnl_pct': date_data['pnl_pct'].sum()
            }
        
        return timeframe_analysis
    
    def generate_comprehensive_report(self):
        """Generate comprehensive analytics report"""
        print("=" * 80)
        print("📊 ADVANCED TRADING ANALYTICS REPORT")
        print("=" * 80)
        print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # Overall performance
        patterns = self.analyze_trade_patterns()
        if patterns:
            print("\n🎯 OVERALL PERFORMANCE METRICS:")
            print(f"   Total Trades: {patterns['total_trades']}")
            print(f"   Win Rate: {patterns['win_rate']:.2%}")
            print(f"   Average Return: {patterns['avg_return_pct']:.2f}%")
            print(f"   Volatility: {patterns['volatility_pct']:.2f}%")
            print(f"   Sharpe Ratio: {patterns['sharpe_ratio']:.2f}")
            print(f"   Sortino Ratio: {patterns['sortino_ratio']:.2f}")
            print(f"   Calmar Ratio: {patterns['calmar_ratio']:.2f}")
            print(f"   Max Drawdown: {patterns['max_drawdown_pct']:.2f}%")
            print(f"   VaR (5%): {patterns['var_5_pct']:.2f}%")
            print(f"   CVaR (5%): {patterns['cvar_5_pct']:.2f}%")
            print(f"   Max Consecutive Wins: {patterns['max_consecutive_wins']}")
            print(f"   Max Consecutive Losses: {patterns['max_consecutive_losses']}")
        
        # Asset analysis
        asset_analysis = self.analyze_by_asset()
        if asset_analysis:
            print("\n📈 PERFORMANCE BY ASSET:")
            for asset, metrics in asset_analysis.items():
                print(f"   {asset}:")
                print(f"     Trades: {metrics['total_trades']}")
                print(f"     Win Rate: {metrics['win_rate']:.2%}")
                print(f"     Avg PnL: {metrics['avg_pnl_pct']:.2f}%")
                print(f"     Total PnL: {metrics['total_pnl_usdt']:.4f} USDT")
        
        # Time analysis
        time_analysis = self.analyze_by_timeframe()
        if time_analysis.get('by_day_of_week'):
            print("\n📅 PERFORMANCE BY DAY OF WEEK:")
            for day, metrics in time_analysis['by_day_of_week'].items():
                print(f"   {day}: {metrics['trades']} trades, {metrics['win_rate']:.2%} win rate, {metrics['avg_pnl_pct']:.2f}% avg PnL")
        
        print("\n" + "=" * 80)
        
        return {
            'overall_performance': patterns,
            'asset_analysis': asset_analysis,
            'timeframe_analysis': time_analysis
        }
    
    def export_report(self, filename=None):
        """Export comprehensive report to JSON"""
        if filename is None:
            filename = f"analytics_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        report_data = {
            'generated_at': datetime.now().isoformat(),
            'overall_performance': self.analyze_trade_patterns(),
            'asset_analysis': self.analyze_by_asset(),
            'timeframe_analysis': self.analyze_by_timeframe()
        }
        
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        print(f"📄 Analytics report exported to: {filename}")
        return filename

if __name__ == "__main__":
    # Run analytics
    analytics = AdvancedAnalytics()
    analytics.generate_comprehensive_report()
    analytics.export_report()
