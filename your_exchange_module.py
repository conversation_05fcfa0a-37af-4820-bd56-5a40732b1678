
# your_exchange_module.py

def get_all_open_positions(exchange):
    """
    Returns a dict of all open positions (with nonzero contracts) for all symbols.
    """
    if exchange is None:
        print("Exchange not connected, cannot fetch open positions.")
        return {}

    open_positions = {}
    try:
        positions = exchange.fetch_positions()  # Gets ALL positions!
        for pos in positions:
            contracts = float(pos.get('contracts', 0))
            symbol = pos.get('symbol')
            if contracts > 0:
                open_positions[symbol] = {
                    "entry_price": float(pos.get('entryPrice', 0)),
                    "amount": contracts,
                    "open_time": pos.get('timestamp', "")
                }
        print(f"Fetched open positions: {open_positions}")  # Debugging statement
        return open_positions
    except Exception as e:
        print(f"Error fetching all open positions: {e}")
        return {}


