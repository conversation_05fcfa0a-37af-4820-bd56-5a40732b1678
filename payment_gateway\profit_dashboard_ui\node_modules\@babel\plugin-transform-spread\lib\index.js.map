{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_helperSkipTransparentExpressionWrappers", "_core", "_default", "exports", "default", "declare", "api", "options", "_api$assumption", "_options$allowArrayLi", "assertVersion", "iterableIsArray", "assumption", "loose", "arrayLikeIsIterable", "allowArrayLike", "getSpreadLiteral", "spread", "scope", "t", "isIdentifier", "argument", "name", "node", "binding", "getBinding", "constant", "path", "isGenericType", "isArrayExpression", "template", "expression", "ast", "args", "helper<PERSON><PERSON>", "unshift", "hub", "addHelper", "callExpression", "hasHole", "elements", "includes", "hasSpread", "nodes", "i", "length", "isSpreadElement", "push", "_props", "arrayExpression", "build", "props", "file", "prop", "spreadLiteral", "visitor", "ArrayExpression", "first", "replaceWith", "shift", "memberExpression", "identifier", "CallExpression", "arguments", "<PERSON><PERSON><PERSON><PERSON>", "skipTransparentExprWrappers", "get", "is<PERSON><PERSON><PERSON>", "buildCodeFrameError", "contextLiteral", "buildUndefinedNode", "callee", "isMemberExpression", "temp", "maybeGenerateMemoised", "object", "assignmentExpression", "cloneNode", "thisExpression", "NewExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport { types as t, template } from \"@babel/core\";\nimport type { File, NodePath, Scope } from \"@babel/core\";\n\ntype ListElement = t.SpreadElement | t.Expression;\n\nexport interface Options {\n  allowArrayLike?: boolean;\n  loose?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const iterableIsArray = api.assumption(\"iterableIsArray\") ?? options.loose;\n  const arrayLikeIsIterable =\n    options.allowArrayLike ?? api.assumption(\"arrayLikeIsIterable\");\n\n  function getSpreadLiteral(\n    spread: t.SpreadElement,\n    scope: Scope,\n  ): t.Expression {\n    if (\n      iterableIsArray &&\n      !t.isIdentifier(spread.argument, { name: \"arguments\" })\n    ) {\n      return spread.argument;\n    } else {\n      const node = spread.argument;\n\n      if (t.isIdentifier(node)) {\n        const binding = scope.getBinding(node.name);\n        if (binding?.constant && binding.path.isGenericType(\"Array\")) {\n          return node;\n        }\n      }\n\n      if (t.isArrayExpression(node)) {\n        return node;\n      }\n\n      if (t.isIdentifier(node, { name: \"arguments\" })) {\n        return template.expression.ast`\n          Array.prototype.slice.call(${node})\n        `;\n      }\n\n      const args = [node];\n      let helperName = \"toConsumableArray\";\n\n      if (arrayLikeIsIterable) {\n        args.unshift(scope.path.hub.addHelper(helperName));\n        helperName = \"maybeArrayLike\";\n      }\n\n      return t.callExpression(scope.path.hub.addHelper(helperName), args);\n    }\n  }\n\n  function hasHole(spread: t.ArrayExpression): boolean {\n    return spread.elements.includes(null);\n  }\n\n  function hasSpread(nodes: Array<t.Node>): boolean {\n    for (let i = 0; i < nodes.length; i++) {\n      if (t.isSpreadElement(nodes[i])) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  function push(_props: Array<ListElement>, nodes: Array<t.Expression>) {\n    if (!_props.length) return _props;\n    nodes.push(t.arrayExpression(_props));\n    return [];\n  }\n\n  function build(\n    props: Array<ListElement>,\n    scope: Scope,\n    file: File,\n  ): t.Expression[] {\n    const nodes: Array<t.Expression> = [];\n    let _props: Array<ListElement> = [];\n\n    for (const prop of props) {\n      if (t.isSpreadElement(prop)) {\n        _props = push(_props, nodes);\n        let spreadLiteral = getSpreadLiteral(prop, scope);\n\n        if (t.isArrayExpression(spreadLiteral) && hasHole(spreadLiteral)) {\n          spreadLiteral = t.callExpression(\n            file.addHelper(\n              process.env.BABEL_8_BREAKING\n                ? \"arrayLikeToArray\"\n                : \"arrayWithoutHoles\",\n            ),\n            [spreadLiteral],\n          );\n        }\n\n        nodes.push(spreadLiteral);\n      } else {\n        _props.push(prop);\n      }\n    }\n\n    push(_props, nodes);\n\n    return nodes;\n  }\n\n  return {\n    name: \"transform-spread\",\n\n    visitor: {\n      ArrayExpression(path): void {\n        const { node, scope } = path;\n        const elements = node.elements;\n        if (!hasSpread(elements)) return;\n\n        const nodes = build(elements, scope, this.file);\n        let first = nodes[0];\n\n        // If there is only one element in the ArrayExpression and\n        // the element was transformed (Array.prototype.slice.call or toConsumableArray)\n        // we know that the transformed code already takes care of cloning the array.\n        // So we can simply return that element.\n        if (\n          nodes.length === 1 &&\n          first !== (elements[0] as t.SpreadElement).argument\n        ) {\n          path.replaceWith(first);\n          return;\n        }\n\n        // If the first element is a ArrayExpression we can directly call\n        // concat on it.\n        // `[..].concat(..)`\n        // If not then we have to use `[].concat(arr)` and not `arr.concat`\n        // because `arr` could be extended/modified (e.g. Immutable) and we do not know exactly\n        // what concat would produce.\n        if (!t.isArrayExpression(first)) {\n          first = t.arrayExpression([]);\n        } else {\n          nodes.shift();\n        }\n\n        path.replaceWith(\n          t.callExpression(\n            t.memberExpression(first, t.identifier(\"concat\")),\n            nodes,\n          ),\n        );\n      },\n      CallExpression(path): void {\n        const { node, scope } = path;\n\n        const args = node.arguments as Array<ListElement>;\n        if (!hasSpread(args)) return;\n        const calleePath = skipTransparentExprWrappers(\n          path.get(\"callee\") as NodePath<t.Expression>,\n        );\n        if (calleePath.isSuper()) {\n          // NOTE: spread and classes have almost the same compat data, so this is very unlikely to happen in practice.\n          throw path.buildCodeFrameError(\n            \"It's not possible to compile spread arguments in `super()` without compiling classes.\\n\" +\n              \"Please add '@babel/plugin-transform-classes' to your Babel configuration.\",\n          );\n        }\n        let contextLiteral: t.Expression | t.Super = scope.buildUndefinedNode();\n        node.arguments = [];\n\n        let nodes: t.Expression[];\n        if (\n          args.length === 1 &&\n          t.isIdentifier((args[0] as t.SpreadElement).argument, {\n            name: \"arguments\",\n          })\n        ) {\n          nodes = [(args[0] as t.SpreadElement).argument];\n        } else {\n          nodes = build(args, scope, this.file);\n        }\n\n        const first = nodes.shift();\n        if (nodes.length) {\n          node.arguments.push(\n            t.callExpression(\n              t.memberExpression(first, t.identifier(\"concat\")),\n              nodes,\n            ),\n          );\n        } else {\n          node.arguments.push(first);\n        }\n\n        const callee = calleePath.node as t.MemberExpression;\n\n        if (t.isMemberExpression(callee)) {\n          const temp = scope.maybeGenerateMemoised(callee.object);\n          if (temp) {\n            callee.object = t.assignmentExpression(\n              \"=\",\n              temp,\n              // object must not be Super when `temp` is an identifier\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n              callee.object as t.Expression,\n            );\n            contextLiteral = temp;\n          } else {\n            contextLiteral = t.cloneNode(callee.object);\n          }\n        }\n\n        // We use the original callee here, to preserve any types/parentheses\n        node.callee = t.memberExpression(\n          node.callee as t.Expression,\n          t.identifier(\"apply\"),\n        );\n        if (t.isSuper(contextLiteral)) {\n          contextLiteral = t.thisExpression();\n        }\n\n        node.arguments.unshift(t.cloneNode(contextLiteral));\n      },\n\n      NewExpression(path): void {\n        const { node, scope } = path;\n        if (!hasSpread(node.arguments)) return;\n\n        const nodes = build(\n          node.arguments as Array<ListElement>,\n          scope,\n          this.file,\n        );\n\n        const first = nodes.shift();\n\n        let args: t.Expression;\n        if (nodes.length) {\n          args = t.callExpression(\n            t.memberExpression(first, t.identifier(\"concat\")),\n            nodes,\n          );\n        } else {\n          args = first;\n        }\n\n        path.replaceWith(\n          t.callExpression(path.hub.addHelper(\"construct\"), [\n            node.callee as t.Expression,\n            args,\n          ]),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,wCAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAAmD,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAUpC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAAA,IAAAC,eAAA,EAAAC,qBAAA;EAChDH,GAAG,CAACI,aAAa,CAAkB,CAAE,CAAC;EAEtC,MAAMC,eAAe,IAAAH,eAAA,GAAGF,GAAG,CAACM,UAAU,CAAC,iBAAiB,CAAC,YAAAJ,eAAA,GAAID,OAAO,CAACM,KAAK;EAC1E,MAAMC,mBAAmB,IAAAL,qBAAA,GACvBF,OAAO,CAACQ,cAAc,YAAAN,qBAAA,GAAIH,GAAG,CAACM,UAAU,CAAC,qBAAqB,CAAC;EAEjE,SAASI,gBAAgBA,CACvBC,MAAuB,EACvBC,KAAY,EACE;IACd,IACEP,eAAe,IACf,CAACQ,WAAC,CAACC,YAAY,CAACH,MAAM,CAACI,QAAQ,EAAE;MAAEC,IAAI,EAAE;IAAY,CAAC,CAAC,EACvD;MACA,OAAOL,MAAM,CAACI,QAAQ;IACxB,CAAC,MAAM;MACL,MAAME,IAAI,GAAGN,MAAM,CAACI,QAAQ;MAE5B,IAAIF,WAAC,CAACC,YAAY,CAACG,IAAI,CAAC,EAAE;QACxB,MAAMC,OAAO,GAAGN,KAAK,CAACO,UAAU,CAACF,IAAI,CAACD,IAAI,CAAC;QAC3C,IAAIE,OAAO,YAAPA,OAAO,CAAEE,QAAQ,IAAIF,OAAO,CAACG,IAAI,CAACC,aAAa,CAAC,OAAO,CAAC,EAAE;UAC5D,OAAOL,IAAI;QACb;MACF;MAEA,IAAIJ,WAAC,CAACU,iBAAiB,CAACN,IAAI,CAAC,EAAE;QAC7B,OAAOA,IAAI;MACb;MAEA,IAAIJ,WAAC,CAACC,YAAY,CAACG,IAAI,EAAE;QAAED,IAAI,EAAE;MAAY,CAAC,CAAC,EAAE;QAC/C,OAAOQ,cAAQ,CAACC,UAAU,CAACC,GAAG;AACtC,uCAAuCT,IAAI;AAC3C,SAAS;MACH;MAEA,MAAMU,IAAI,GAAG,CAACV,IAAI,CAAC;MACnB,IAAIW,UAAU,GAAG,mBAAmB;MAEpC,IAAIpB,mBAAmB,EAAE;QACvBmB,IAAI,CAACE,OAAO,CAACjB,KAAK,CAACS,IAAI,CAACS,GAAG,CAACC,SAAS,CAACH,UAAU,CAAC,CAAC;QAClDA,UAAU,GAAG,gBAAgB;MAC/B;MAEA,OAAOf,WAAC,CAACmB,cAAc,CAACpB,KAAK,CAACS,IAAI,CAACS,GAAG,CAACC,SAAS,CAACH,UAAU,CAAC,EAAED,IAAI,CAAC;IACrE;EACF;EAEA,SAASM,OAAOA,CAACtB,MAAyB,EAAW;IACnD,OAAOA,MAAM,CAACuB,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC;EACvC;EAEA,SAASC,SAASA,CAACC,KAAoB,EAAW;IAChD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIzB,WAAC,CAAC2B,eAAe,CAACH,KAAK,CAACC,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEA,SAASG,IAAIA,CAACC,MAA0B,EAAEL,KAA0B,EAAE;IACpE,IAAI,CAACK,MAAM,CAACH,MAAM,EAAE,OAAOG,MAAM;IACjCL,KAAK,CAACI,IAAI,CAAC5B,WAAC,CAAC8B,eAAe,CAACD,MAAM,CAAC,CAAC;IACrC,OAAO,EAAE;EACX;EAEA,SAASE,KAAKA,CACZC,KAAyB,EACzBjC,KAAY,EACZkC,IAAU,EACM;IAChB,MAAMT,KAA0B,GAAG,EAAE;IACrC,IAAIK,MAA0B,GAAG,EAAE;IAEnC,KAAK,MAAMK,IAAI,IAAIF,KAAK,EAAE;MACxB,IAAIhC,WAAC,CAAC2B,eAAe,CAACO,IAAI,CAAC,EAAE;QAC3BL,MAAM,GAAGD,IAAI,CAACC,MAAM,EAAEL,KAAK,CAAC;QAC5B,IAAIW,aAAa,GAAGtC,gBAAgB,CAACqC,IAAI,EAAEnC,KAAK,CAAC;QAEjD,IAAIC,WAAC,CAACU,iBAAiB,CAACyB,aAAa,CAAC,IAAIf,OAAO,CAACe,aAAa,CAAC,EAAE;UAChEA,aAAa,GAAGnC,WAAC,CAACmB,cAAc,CAC9Bc,IAAI,CAACf,SAAS,CAGR,mBACN,CAAC,EACD,CAACiB,aAAa,CAChB,CAAC;QACH;QAEAX,KAAK,CAACI,IAAI,CAACO,aAAa,CAAC;MAC3B,CAAC,MAAM;QACLN,MAAM,CAACD,IAAI,CAACM,IAAI,CAAC;MACnB;IACF;IAEAN,IAAI,CAACC,MAAM,EAAEL,KAAK,CAAC;IAEnB,OAAOA,KAAK;EACd;EAEA,OAAO;IACLrB,IAAI,EAAE,kBAAkB;IAExBiC,OAAO,EAAE;MACPC,eAAeA,CAAC7B,IAAI,EAAQ;QAC1B,MAAM;UAAEJ,IAAI;UAAEL;QAAM,CAAC,GAAGS,IAAI;QAC5B,MAAMa,QAAQ,GAAGjB,IAAI,CAACiB,QAAQ;QAC9B,IAAI,CAACE,SAAS,CAACF,QAAQ,CAAC,EAAE;QAE1B,MAAMG,KAAK,GAAGO,KAAK,CAACV,QAAQ,EAAEtB,KAAK,EAAE,IAAI,CAACkC,IAAI,CAAC;QAC/C,IAAIK,KAAK,GAAGd,KAAK,CAAC,CAAC,CAAC;QAMpB,IACEA,KAAK,CAACE,MAAM,KAAK,CAAC,IAClBY,KAAK,KAAMjB,QAAQ,CAAC,CAAC,CAAC,CAAqBnB,QAAQ,EACnD;UACAM,IAAI,CAAC+B,WAAW,CAACD,KAAK,CAAC;UACvB;QACF;QAQA,IAAI,CAACtC,WAAC,CAACU,iBAAiB,CAAC4B,KAAK,CAAC,EAAE;UAC/BA,KAAK,GAAGtC,WAAC,CAAC8B,eAAe,CAAC,EAAE,CAAC;QAC/B,CAAC,MAAM;UACLN,KAAK,CAACgB,KAAK,CAAC,CAAC;QACf;QAEAhC,IAAI,CAAC+B,WAAW,CACdvC,WAAC,CAACmB,cAAc,CACdnB,WAAC,CAACyC,gBAAgB,CAACH,KAAK,EAAEtC,WAAC,CAAC0C,UAAU,CAAC,QAAQ,CAAC,CAAC,EACjDlB,KACF,CACF,CAAC;MACH,CAAC;MACDmB,cAAcA,CAACnC,IAAI,EAAQ;QACzB,MAAM;UAAEJ,IAAI;UAAEL;QAAM,CAAC,GAAGS,IAAI;QAE5B,MAAMM,IAAI,GAAGV,IAAI,CAACwC,SAA+B;QACjD,IAAI,CAACrB,SAAS,CAACT,IAAI,CAAC,EAAE;QACtB,MAAM+B,UAAU,GAAG,IAAAC,oEAA2B,EAC5CtC,IAAI,CAACuC,GAAG,CAAC,QAAQ,CACnB,CAAC;QACD,IAAIF,UAAU,CAACG,OAAO,CAAC,CAAC,EAAE;UAExB,MAAMxC,IAAI,CAACyC,mBAAmB,CAC5B,yFAAyF,GACvF,2EACJ,CAAC;QACH;QACA,IAAIC,cAAsC,GAAGnD,KAAK,CAACoD,kBAAkB,CAAC,CAAC;QACvE/C,IAAI,CAACwC,SAAS,GAAG,EAAE;QAEnB,IAAIpB,KAAqB;QACzB,IACEV,IAAI,CAACY,MAAM,KAAK,CAAC,IACjB1B,WAAC,CAACC,YAAY,CAAEa,IAAI,CAAC,CAAC,CAAC,CAAqBZ,QAAQ,EAAE;UACpDC,IAAI,EAAE;QACR,CAAC,CAAC,EACF;UACAqB,KAAK,GAAG,CAAEV,IAAI,CAAC,CAAC,CAAC,CAAqBZ,QAAQ,CAAC;QACjD,CAAC,MAAM;UACLsB,KAAK,GAAGO,KAAK,CAACjB,IAAI,EAAEf,KAAK,EAAE,IAAI,CAACkC,IAAI,CAAC;QACvC;QAEA,MAAMK,KAAK,GAAGd,KAAK,CAACgB,KAAK,CAAC,CAAC;QAC3B,IAAIhB,KAAK,CAACE,MAAM,EAAE;UAChBtB,IAAI,CAACwC,SAAS,CAAChB,IAAI,CACjB5B,WAAC,CAACmB,cAAc,CACdnB,WAAC,CAACyC,gBAAgB,CAACH,KAAK,EAAEtC,WAAC,CAAC0C,UAAU,CAAC,QAAQ,CAAC,CAAC,EACjDlB,KACF,CACF,CAAC;QACH,CAAC,MAAM;UACLpB,IAAI,CAACwC,SAAS,CAAChB,IAAI,CAACU,KAAK,CAAC;QAC5B;QAEA,MAAMc,MAAM,GAAGP,UAAU,CAACzC,IAA0B;QAEpD,IAAIJ,WAAC,CAACqD,kBAAkB,CAACD,MAAM,CAAC,EAAE;UAChC,MAAME,IAAI,GAAGvD,KAAK,CAACwD,qBAAqB,CAACH,MAAM,CAACI,MAAM,CAAC;UACvD,IAAIF,IAAI,EAAE;YACRF,MAAM,CAACI,MAAM,GAAGxD,WAAC,CAACyD,oBAAoB,CACpC,GAAG,EACHH,IAAI,EAGJF,MAAM,CAACI,MACT,CAAC;YACDN,cAAc,GAAGI,IAAI;UACvB,CAAC,MAAM;YACLJ,cAAc,GAAGlD,WAAC,CAAC0D,SAAS,CAACN,MAAM,CAACI,MAAM,CAAC;UAC7C;QACF;QAGApD,IAAI,CAACgD,MAAM,GAAGpD,WAAC,CAACyC,gBAAgB,CAC9BrC,IAAI,CAACgD,MAAM,EACXpD,WAAC,CAAC0C,UAAU,CAAC,OAAO,CACtB,CAAC;QACD,IAAI1C,WAAC,CAACgD,OAAO,CAACE,cAAc,CAAC,EAAE;UAC7BA,cAAc,GAAGlD,WAAC,CAAC2D,cAAc,CAAC,CAAC;QACrC;QAEAvD,IAAI,CAACwC,SAAS,CAAC5B,OAAO,CAAChB,WAAC,CAAC0D,SAAS,CAACR,cAAc,CAAC,CAAC;MACrD,CAAC;MAEDU,aAAaA,CAACpD,IAAI,EAAQ;QACxB,MAAM;UAAEJ,IAAI;UAAEL;QAAM,CAAC,GAAGS,IAAI;QAC5B,IAAI,CAACe,SAAS,CAACnB,IAAI,CAACwC,SAAS,CAAC,EAAE;QAEhC,MAAMpB,KAAK,GAAGO,KAAK,CACjB3B,IAAI,CAACwC,SAAS,EACd7C,KAAK,EACL,IAAI,CAACkC,IACP,CAAC;QAED,MAAMK,KAAK,GAAGd,KAAK,CAACgB,KAAK,CAAC,CAAC;QAE3B,IAAI1B,IAAkB;QACtB,IAAIU,KAAK,CAACE,MAAM,EAAE;UAChBZ,IAAI,GAAGd,WAAC,CAACmB,cAAc,CACrBnB,WAAC,CAACyC,gBAAgB,CAACH,KAAK,EAAEtC,WAAC,CAAC0C,UAAU,CAAC,QAAQ,CAAC,CAAC,EACjDlB,KACF,CAAC;QACH,CAAC,MAAM;UACLV,IAAI,GAAGwB,KAAK;QACd;QAEA9B,IAAI,CAAC+B,WAAW,CACdvC,WAAC,CAACmB,cAAc,CAACX,IAAI,CAACS,GAAG,CAACC,SAAS,CAAC,WAAW,CAAC,EAAE,CAChDd,IAAI,CAACgD,MAAM,EACXtC,IAAI,CACL,CACH,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}