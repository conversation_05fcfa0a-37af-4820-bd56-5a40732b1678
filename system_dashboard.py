#!/usr/bin/env python3
"""
System Dashboard for Quantum Trading System
Real-time monitoring and control interface
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objs as go
import plotly.express as px
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

try:
    from performance_optimizer import performance_monitor, smart_cache, optimize_system
    from advanced_analytics import AdvancedAnalytics
    ENHANCED_FEATURES_AVAILABLE = True
except ImportError:
    ENHANCED_FEATURES_AVAILABLE = False

# Initialize Dash app
app = dash.Dash(__name__, external_stylesheets=['https://codepen.io/chriddyp/pen/bWLwgP.css'])

def create_performance_chart():
    """Create performance monitoring chart"""
    if not ENHANCED_FEATURES_AVAILABLE:
        return go.Figure().add_annotation(text="Enhanced features not available", 
                                        xref="paper", yref="paper", x=0.5, y=0.5)
    
    try:
        report = performance_monitor.get_performance_report()
        
        # Memory usage chart
        memory_data = performance_monitor.metrics['memory_usage']
        if memory_data:
            df = pd.DataFrame(memory_data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df['memory_percent'],
                mode='lines',
                name='Memory Usage %',
                line=dict(color='blue')
            ))
            
            fig.update_layout(
                title='System Memory Usage',
                xaxis_title='Time',
                yaxis_title='Memory Usage (%)',
                height=300
            )
            
            return fig
    except Exception as e:
        print(f"Error creating performance chart: {e}")
    
    return go.Figure()

def create_api_calls_chart():
    """Create API calls monitoring chart"""
    if not ENHANCED_FEATURES_AVAILABLE:
        return go.Figure().add_annotation(text="Enhanced features not available", 
                                        xref="paper", yref="paper", x=0.5, y=0.5)
    
    try:
        report = performance_monitor.get_performance_report()
        api_calls = report.get('api_calls_by_endpoint', {})
        
        if api_calls:
            endpoints = list(api_calls.keys())
            counts = list(api_calls.values())
            
            fig = go.Figure(data=[
                go.Bar(x=endpoints, y=counts, marker_color='lightblue')
            ])
            
            fig.update_layout(
                title='API Calls by Endpoint',
                xaxis_title='Endpoint',
                yaxis_title='Call Count',
                height=300
            )
            
            return fig
    except Exception as e:
        print(f"Error creating API calls chart: {e}")
    
    return go.Figure()

def create_trading_analytics_chart():
    """Create trading analytics chart"""
    try:
        analytics = AdvancedAnalytics()
        patterns = analytics.analyze_trade_patterns()
        
        if patterns and patterns.get('total_trades', 0) > 0:
            # Create win/loss pie chart
            fig = go.Figure(data=[
                go.Pie(
                    labels=['Winning Trades', 'Losing Trades'],
                    values=[patterns['winning_trades'], patterns['losing_trades']],
                    hole=0.3,
                    marker_colors=['green', 'red']
                )
            ])
            
            fig.update_layout(
                title=f"Trade Distribution (Total: {patterns['total_trades']})",
                height=300
            )
            
            return fig
    except Exception as e:
        print(f"Error creating trading analytics chart: {e}")
    
    return go.Figure().add_annotation(text="No trading data available", 
                                    xref="paper", yref="paper", x=0.5, y=0.5)

# Layout
app.layout = html.Div([
    html.H1("🚀 Quantum Trading System Dashboard", 
            style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': 30}),
    
    # Auto-refresh interval
    dcc.Interval(
        id='interval-component',
        interval=10*1000,  # Update every 10 seconds
        n_intervals=0
    ),
    
    # System Status Row
    html.Div([
        html.Div([
            html.H3("📊 System Performance", style={'color': '#34495e'}),
            html.Div(id='system-stats'),
        ], className='six columns'),
        
        html.Div([
            html.H3("💾 Cache Statistics", style={'color': '#34495e'}),
            html.Div(id='cache-stats'),
        ], className='six columns'),
    ], className='row', style={'marginBottom': 30}),
    
    # Charts Row 1
    html.Div([
        html.Div([
            dcc.Graph(id='performance-chart')
        ], className='six columns'),
        
        html.Div([
            dcc.Graph(id='api-calls-chart')
        ], className='six columns'),
    ], className='row', style={'marginBottom': 30}),
    
    # Charts Row 2
    html.Div([
        html.Div([
            dcc.Graph(id='trading-analytics-chart')
        ], className='six columns'),
        
        html.Div([
            html.H3("🎯 Quick Actions", style={'color': '#34495e'}),
            html.Button('🔄 Optimize System', id='optimize-btn', 
                       style={'backgroundColor': '#3498db', 'color': 'white', 'border': 'none', 
                             'padding': '10px 20px', 'margin': '5px', 'borderRadius': '5px'}),
            html.Button('📊 Generate Analytics Report', id='analytics-btn',
                       style={'backgroundColor': '#2ecc71', 'color': 'white', 'border': 'none', 
                             'padding': '10px 20px', 'margin': '5px', 'borderRadius': '5px'}),
            html.Button('💾 Clear Cache', id='clear-cache-btn',
                       style={'backgroundColor': '#e74c3c', 'color': 'white', 'border': 'none', 
                             'padding': '10px 20px', 'margin': '5px', 'borderRadius': '5px'}),
            html.Div(id='action-output', style={'marginTop': 20})
        ], className='six columns'),
    ], className='row'),
    
    # Footer
    html.Hr(),
    html.P(f"🤖 Quantum Trading System Dashboard - Enhanced Features: {'✅ Available' if ENHANCED_FEATURES_AVAILABLE else '❌ Not Available'}", 
           style={'textAlign': 'center', 'color': '#7f8c8d', 'marginTop': 30})
])

@callback(
    Output('system-stats', 'children'),
    Input('interval-component', 'n_intervals')
)
def update_system_stats(n):
    if not ENHANCED_FEATURES_AVAILABLE:
        return html.P("Enhanced features not available", style={'color': 'orange'})
    
    try:
        report = performance_monitor.get_performance_report()
        
        return html.Div([
            html.P(f"⏱️ Uptime: {report['system_uptime_hours']:.1f} hours"),
            html.P(f"🧠 Memory: {report['average_memory_usage']:.1f}%"),
            html.P(f"⚡ CPU: {report['average_cpu_usage']:.1f}%"),
            html.P(f"📡 API Calls: {report['total_api_calls']}"),
            html.P(f"⚡ Avg Latency: {report['average_trade_latency_ms']:.1f}ms"),
            html.P(f"❌ Errors: {report['total_errors']}")
        ])
    except Exception as e:
        return html.P(f"Error: {e}", style={'color': 'red'})

@callback(
    Output('cache-stats', 'children'),
    Input('interval-component', 'n_intervals')
)
def update_cache_stats(n):
    if not ENHANCED_FEATURES_AVAILABLE:
        return html.P("Enhanced features not available", style={'color': 'orange'})
    
    try:
        stats = smart_cache.get_stats()
        
        return html.Div([
            html.P(f"📦 Total Entries: {stats['total_entries']}"),
            html.P(f"✅ Valid Entries: {stats['valid_entries']}"),
            html.P(f"❌ Expired: {stats['expired_entries']}"),
            html.P(f"💾 Size: {stats['cache_size_mb']:.2f}MB"),
            html.P(f"🎯 Hit Rate: {stats['hit_rate']:.2%}")
        ])
    except Exception as e:
        return html.P(f"Error: {e}", style={'color': 'red'})

@callback(
    Output('performance-chart', 'figure'),
    Input('interval-component', 'n_intervals')
)
def update_performance_chart(n):
    return create_performance_chart()

@callback(
    Output('api-calls-chart', 'figure'),
    Input('interval-component', 'n_intervals')
)
def update_api_calls_chart(n):
    return create_api_calls_chart()

@callback(
    Output('trading-analytics-chart', 'figure'),
    Input('interval-component', 'n_intervals')
)
def update_trading_analytics_chart(n):
    return create_trading_analytics_chart()

@callback(
    Output('action-output', 'children'),
    [Input('optimize-btn', 'n_clicks'),
     Input('analytics-btn', 'n_clicks'),
     Input('clear-cache-btn', 'n_clicks')]
)
def handle_actions(optimize_clicks, analytics_clicks, clear_clicks):
    ctx = dash.callback_context
    if not ctx.triggered:
        return ""
    
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    try:
        if button_id == 'optimize-btn' and ENHANCED_FEATURES_AVAILABLE:
            optimize_system()
            return html.P("✅ System optimization completed!", style={'color': 'green'})
        
        elif button_id == 'analytics-btn':
            analytics = AdvancedAnalytics()
            filename = analytics.export_report()
            return html.P(f"✅ Analytics report exported: {filename}", style={'color': 'green'})
        
        elif button_id == 'clear-cache-btn' and ENHANCED_FEATURES_AVAILABLE:
            smart_cache.clear()
            return html.P("✅ Cache cleared successfully!", style={'color': 'green'})
        
        else:
            return html.P("❌ Action not available", style={'color': 'orange'})
            
    except Exception as e:
        return html.P(f"❌ Error: {e}", style={'color': 'red'})

if __name__ == '__main__':
    print("🚀 Starting Quantum Trading System Dashboard...")
    print(f"📊 Enhanced Features: {'Available' if ENHANCED_FEATURES_AVAILABLE else 'Not Available'}")
    print("🌐 Dashboard will be available at: http://127.0.0.1:8051")
    
    app.run_server(debug=True, port=8051)
