{"version": 3, "names": ["_t", "require", "STATEMENT_OR_BLOCK_KEYS", "VISITOR_KEYS", "isBlockStatement", "isExpression", "isIdentifier", "isLiteral", "isStringLiteral", "isType", "matchesPattern", "_matchesPattern", "pattern", "allowPartial", "node", "exports", "has", "key", "_this$node", "val", "Array", "isArray", "length", "isStatic", "scope", "is", "isnt", "equals", "value", "isNodeType", "type", "canHaveVariableDeclarationOrExpression", "parentPath", "isFor", "canSwapBetweenExpressionAndStatement", "replacement", "isArrowFunctionExpression", "isCompletionRecord", "allowInsideFunction", "path", "first", "container", "isFunction", "isProgram", "isDoExpression", "isStatementOrBlock", "isLabeledStatement", "includes", "referencesImport", "moduleSource", "importName", "isReferencedIdentifier", "isJSXMemberExpression", "property", "name", "isMemberExpression", "isOptionalMemberExpression", "computed", "object", "get", "binding", "getBinding", "kind", "parent", "isImportDeclaration", "source", "isImportDefaultSpecifier", "isImportNamespaceSpecifier", "isImportSpecifier", "imported", "getSource", "end", "code", "hub", "getCode", "slice", "start", "willIMaybeExecuteBefore", "target", "_guessExecutionStatusRelativeTo", "getOuterFunction", "getFunctionParent", "getProgramParent", "isExecutionUncertain", "isExecutionUncertainInList", "paths", "maxIndex", "i", "parent<PERSON><PERSON>", "SYMBOL_CHECKING", "Symbol", "_guessExecutionStatusRelativeToCached", "Map", "base", "cache", "func<PERSON>arent", "this", "_guessExecutionStatusRelativeToDifferentFunctionsCached", "getAncestry", "commonPath", "commonIndex", "indexOf", "Error", "divergence", "<PERSON><PERSON><PERSON>", "keys", "keyPosition", "_guessExecutionStatusRelativeToDifferentFunctionsInternal", "isFunctionDeclaration", "isExportDeclaration", "id", "references", "referencePaths", "allStatus", "childOfFunction", "find", "isCallExpression", "status", "nodeMap", "cached", "set", "result", "resolve", "dangerous", "resolved", "_resolve", "call", "_resolved", "push", "isVariableDeclarator", "constant", "ret", "isTypeCastExpression", "<PERSON><PERSON><PERSON>", "toCom<PERSON><PERSON>ey", "targetName", "isObjectExpression", "props", "prop", "isProperty", "match", "isArrayExpression", "isNaN", "elems", "elem", "isConstantExpression", "isRegExpLiteral", "isTemplateLiteral", "every", "expression", "isUnaryExpression", "operator", "isBinaryExpression", "hasBinding", "noGlobals", "arguments", "isInStrictMode", "strictParent", "sourceType", "isClass", "body", "directive", "directives"], "sources": ["../../src/path/introspection.ts"], "sourcesContent": ["// This file contains methods responsible for introspecting the current path for certain values.\n\nimport type <PERSON>de<PERSON><PERSON> from \"./index.ts\";\nimport {\n  STATEMENT_OR_BLOCK_KEYS,\n  VISITOR_KEYS,\n  isBlockStatement,\n  isExpression,\n  isIdentifier,\n  isLiteral,\n  isStringLiteral,\n  isType,\n  matchesPattern as _matchesPattern,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\n/**\n * Match the current node if it matches the provided `pattern`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\n\nexport function matchesPattern(\n  this: NodePath,\n  pattern: string,\n  allowPartial?: boolean,\n): boolean {\n  return _matchesPattern(this.node, pattern, allowPartial);\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * Check whether we have the input `key`. If the `key` references an array then we check\n   * if the array has any items, otherwise we just check if it's falsy.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.has = function has<N extends t.Node>(\n    this: NodePath<N>,\n    key: keyof N,\n  ): boolean {\n    const val = (this.node as N)?.[key];\n    if (val && Array.isArray(val)) {\n      return !!val.length;\n    } else {\n      return !!val;\n    }\n  };\n}\n\nexport function isStatic(this: NodePath): boolean {\n  return this.scope.isStatic(this.node);\n}\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  /**\n   * Alias of `has`.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.is = exports.has;\n\n  /**\n   * Opposite of `has`.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.isnt = function isnt<N extends t.Node>(\n    this: NodePath<N>,\n    key: keyof N,\n  ): boolean {\n    // @ts-expect-error Babel 7\n    return !this.has(key);\n  };\n\n  /**\n   * Check whether the path node `key` strict equals `value`.\n   */\n  // eslint-disable-next-line no-restricted-globals\n  exports.equals = function equals<N extends t.Node>(\n    this: NodePath<N>,\n    key: keyof N,\n    value: any,\n  ): boolean {\n    return (this.node as N)[key] === value;\n  };\n}\n\n/**\n * Check the type against our stored internal type of the node. This is handy when a node has\n * been removed yet we still internally know the type and need it to calculate node replacement.\n */\n\nexport function isNodeType(this: NodePath, type: string): boolean {\n  return isType(this.type, type);\n}\n\n/**\n * This checks whether or not we're in one of the following positions:\n *\n *   for (KEY in right);\n *   for (KEY;;);\n *\n * This is because these spots allow VariableDeclarations AND normal expressions so we need\n * to tell the path replacement that it's ok to replace this with an expression.\n */\n\nexport function canHaveVariableDeclarationOrExpression(this: NodePath) {\n  return (\n    (this.key === \"init\" || this.key === \"left\") && this.parentPath.isFor()\n  );\n}\n\n/**\n * This checks whether we are swapping an arrow function's body between an\n * expression and a block statement (or vice versa).\n *\n * This is because arrow functions may implicitly return an expression, which\n * is the same as containing a block statement.\n */\n\nexport function canSwapBetweenExpressionAndStatement(\n  this: NodePath,\n  replacement: t.Node,\n): boolean {\n  if (this.key !== \"body\" || !this.parentPath.isArrowFunctionExpression()) {\n    return false;\n  }\n\n  if (this.isExpression()) {\n    return isBlockStatement(replacement);\n  } else if (this.isBlockStatement()) {\n    return isExpression(replacement);\n  }\n\n  return false;\n}\n\n/**\n * Check whether the current path references a completion record\n */\n\nexport function isCompletionRecord(\n  this: NodePath,\n  allowInsideFunction?: boolean,\n): boolean {\n  let path = this;\n  let first = true;\n\n  do {\n    const { type, container } = path;\n\n    // we're in a function so can't be a completion record\n    if (!first && (path.isFunction() || type === \"StaticBlock\")) {\n      return !!allowInsideFunction;\n    }\n\n    first = false;\n\n    // check to see if we're the last item in the container and if we are\n    // we're a completion record!\n    if (Array.isArray(container) && path.key !== container.length - 1) {\n      return false;\n    }\n  } while (\n    (path = path.parentPath) &&\n    !path.isProgram() &&\n    !path.isDoExpression()\n  );\n\n  return true;\n}\n\n/**\n * Check whether or not the current `key` allows either a single statement or block statement\n * so we can explode it if necessary.\n */\n\nexport function isStatementOrBlock(this: NodePath): boolean {\n  if (\n    this.parentPath.isLabeledStatement() ||\n    isBlockStatement(this.container as t.Node)\n  ) {\n    return false;\n  } else {\n    return STATEMENT_OR_BLOCK_KEYS.includes(this.key as string);\n  }\n}\n\n/**\n * Check if the currently assigned path references the `importName` of `moduleSource`.\n */\n\nexport function referencesImport(\n  this: NodePath,\n  moduleSource: string,\n  importName: string,\n): boolean {\n  if (!this.isReferencedIdentifier()) {\n    if (\n      (this.isJSXMemberExpression() &&\n        this.node.property.name === importName) ||\n      ((this.isMemberExpression() || this.isOptionalMemberExpression()) &&\n        (this.node.computed\n          ? isStringLiteral(this.node.property, { value: importName })\n          : (this.node.property as t.Identifier).name === importName))\n    ) {\n      const object = (\n        this as NodePath<t.MemberExpression | t.OptionalMemberExpression>\n      ).get(\"object\");\n      return (\n        object.isReferencedIdentifier() &&\n        object.referencesImport(moduleSource, \"*\")\n      );\n    }\n\n    return false;\n  }\n\n  const binding = this.scope.getBinding((this.node as t.Identifier).name);\n  if (!binding || binding.kind !== \"module\") return false;\n\n  const path = binding.path;\n  const parent = path.parentPath;\n  if (!parent.isImportDeclaration()) return false;\n\n  // check moduleSource\n  if (parent.node.source.value === moduleSource) {\n    if (!importName) return true;\n  } else {\n    return false;\n  }\n\n  if (path.isImportDefaultSpecifier() && importName === \"default\") {\n    return true;\n  }\n\n  if (path.isImportNamespaceSpecifier() && importName === \"*\") {\n    return true;\n  }\n\n  if (\n    path.isImportSpecifier() &&\n    isIdentifier(path.node.imported, { name: importName })\n  ) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Get the source code associated with this node.\n */\n\nexport function getSource(this: NodePath): string {\n  const node = this.node;\n  if (node.end) {\n    const code = this.hub.getCode();\n    if (code) return code.slice(node.start, node.end);\n  }\n  return \"\";\n}\n\nexport function willIMaybeExecuteBefore(\n  this: NodePath,\n  target: NodePath,\n): boolean {\n  return this._guessExecutionStatusRelativeTo(target) !== \"after\";\n}\n\nfunction getOuterFunction(path: NodePath) {\n  return path.isProgram()\n    ? path\n    : (\n        path.parentPath.scope.getFunctionParent() ||\n        path.parentPath.scope.getProgramParent()\n      ).path;\n}\n\nfunction isExecutionUncertain(type: t.Node[\"type\"], key: string) {\n  switch (type) {\n    // a && FOO\n    // a || FOO\n    case \"LogicalExpression\":\n      return key === \"right\";\n\n    // a ? FOO : FOO\n    // if (a) FOO; else FOO;\n    case \"ConditionalExpression\":\n    case \"IfStatement\":\n      return key === \"consequent\" || key === \"alternate\";\n\n    // while (a) FOO;\n    case \"WhileStatement\":\n    case \"DoWhileStatement\":\n    case \"ForInStatement\":\n    case \"ForOfStatement\":\n      return key === \"body\";\n\n    // for (a; b; FOO) FOO;\n    case \"ForStatement\":\n      return key === \"body\" || key === \"update\";\n\n    // switch (a) { FOO }\n    case \"SwitchStatement\":\n      return key === \"cases\";\n\n    // try { a } catch FOO finally { b }\n    case \"TryStatement\":\n      return key === \"handler\";\n\n    // var [ x = FOO ]\n    case \"AssignmentPattern\":\n      return key === \"right\";\n\n    // a?.[FOO]\n    case \"OptionalMemberExpression\":\n      return key === \"property\";\n\n    // a?.(FOO)\n    case \"OptionalCallExpression\":\n      return key === \"arguments\";\n\n    default:\n      return false;\n  }\n}\n\nfunction isExecutionUncertainInList(paths: NodePath[], maxIndex: number) {\n  for (let i = 0; i < maxIndex; i++) {\n    const path = paths[i];\n    if (isExecutionUncertain(path.parent.type, path.parentKey)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// TODO(Babel 8)\n// This can be { before: boolean, after: boolean, unknown: boolean }.\n// This allows transforms like the tdz one to treat cases when the status\n// is both before and unknown/after like if it were before.\ntype RelativeExecutionStatus = \"before\" | \"after\" | \"unknown\";\n\n// Used to avoid infinite recursion in cases like\n//   function f() { if (false) f(); }\n//   f();\n// It also works with indirect recursion.\nconst SYMBOL_CHECKING = Symbol();\n\ntype ExecutionStatusCache = Map<\n  t.Node,\n  Map<t.Node, RelativeExecutionStatus | typeof SYMBOL_CHECKING>\n>;\n\n/**\n * Given a `target` check the execution status of it relative to the current path.\n *\n * \"Execution status\" simply refers to where or not we **think** this will execute\n * before or after the input `target` element.\n */\n\nexport function _guessExecutionStatusRelativeTo(\n  this: NodePath,\n  target: NodePath,\n): RelativeExecutionStatus {\n  return _guessExecutionStatusRelativeToCached(this, target, new Map());\n}\n\nfunction _guessExecutionStatusRelativeToCached(\n  base: NodePath,\n  target: NodePath,\n  cache: ExecutionStatusCache,\n): RelativeExecutionStatus {\n  // check if the two paths are in different functions, we can't track execution of these\n  const funcParent = {\n    this: getOuterFunction(base),\n    target: getOuterFunction(target),\n  };\n\n  // here we check the `node` equality as sometimes we may have different paths for the\n  // same node due to path thrashing\n  if (funcParent.target.node !== funcParent.this.node) {\n    return _guessExecutionStatusRelativeToDifferentFunctionsCached(\n      base,\n      funcParent.target,\n      cache,\n    );\n  }\n\n  const paths = {\n    target: target.getAncestry(),\n    this: base.getAncestry(),\n  };\n\n  // If this is an ancestor of the target path,\n  // e.g. f(g); where this is f and target is g.\n  if (paths.target.includes(base)) return \"after\";\n  if (paths.this.includes(target)) return \"before\";\n\n  // get ancestor where the branches intersect\n  let commonPath;\n  const commonIndex = { target: 0, this: 0 };\n\n  while (!commonPath && commonIndex.this < paths.this.length) {\n    const path = paths.this[commonIndex.this];\n    commonIndex.target = paths.target.indexOf(path);\n    if (commonIndex.target >= 0) {\n      commonPath = path;\n    } else {\n      commonIndex.this++;\n    }\n  }\n\n  if (!commonPath) {\n    throw new Error(\n      \"Internal Babel error - The two compared nodes\" +\n        \" don't appear to belong to the same program.\",\n    );\n  }\n\n  if (\n    isExecutionUncertainInList(paths.this, commonIndex.this - 1) ||\n    isExecutionUncertainInList(paths.target, commonIndex.target - 1)\n  ) {\n    return \"unknown\";\n  }\n\n  const divergence = {\n    this: paths.this[commonIndex.this - 1],\n    target: paths.target[commonIndex.target - 1],\n  };\n\n  // container list so let's see which one is after the other\n  // e.g. [ THIS, TARGET ]\n  if (\n    divergence.target.listKey &&\n    divergence.this.listKey &&\n    divergence.target.container === divergence.this.container\n  ) {\n    return divergence.target.key > divergence.this.key ? \"before\" : \"after\";\n  }\n\n  // otherwise we're associated by a parent node, check which key comes before the other\n  const keys = VISITOR_KEYS[commonPath.type];\n  const keyPosition = {\n    this: keys.indexOf(divergence.this.parentKey),\n    target: keys.indexOf(divergence.target.parentKey),\n  };\n  return keyPosition.target > keyPosition.this ? \"before\" : \"after\";\n}\n\nfunction _guessExecutionStatusRelativeToDifferentFunctionsInternal(\n  base: NodePath,\n  target: NodePath,\n  cache: ExecutionStatusCache,\n): RelativeExecutionStatus {\n  if (!target.isFunctionDeclaration()) {\n    if (\n      _guessExecutionStatusRelativeToCached(base, target, cache) === \"before\"\n    ) {\n      return \"before\";\n    }\n    return \"unknown\";\n  } else if (target.parentPath.isExportDeclaration()) {\n    return \"unknown\";\n  }\n\n  // so we're in a completely different function, if this is a function declaration\n  // then we can be a bit smarter and handle cases where the function is either\n  // a. not called at all (part of an export)\n  // b. called directly\n  const binding = target.scope.getBinding(target.node.id.name);\n\n  // no references!\n  if (!binding.references) return \"before\";\n\n  const referencePaths: Array<NodePath> = binding.referencePaths;\n\n  let allStatus;\n\n  // verify that all the calls have the same execution status\n  for (const path of referencePaths) {\n    // if a reference is a child of the function we're checking against then we can\n    // safely ignore it\n    const childOfFunction = !!path.find(path => path.node === target.node);\n    if (childOfFunction) continue;\n\n    if (path.key !== \"callee\" || !path.parentPath.isCallExpression()) {\n      // This function is passed as a reference, so we don't\n      // know when it will be called.\n      return \"unknown\";\n    }\n\n    const status = _guessExecutionStatusRelativeToCached(base, path, cache);\n\n    if (allStatus && allStatus !== status) {\n      return \"unknown\";\n    } else {\n      allStatus = status;\n    }\n  }\n\n  return allStatus;\n}\n\nfunction _guessExecutionStatusRelativeToDifferentFunctionsCached(\n  base: NodePath,\n  target: NodePath,\n  cache: ExecutionStatusCache,\n): RelativeExecutionStatus {\n  let nodeMap = cache.get(base.node);\n  let cached;\n\n  if (!nodeMap) {\n    cache.set(base.node, (nodeMap = new Map()));\n  } else if ((cached = nodeMap.get(target.node))) {\n    if (cached === SYMBOL_CHECKING) {\n      return \"unknown\";\n    }\n    return cached;\n  }\n\n  nodeMap.set(target.node, SYMBOL_CHECKING);\n\n  const result = _guessExecutionStatusRelativeToDifferentFunctionsInternal(\n    base,\n    target,\n    cache,\n  );\n\n  nodeMap.set(target.node, result);\n  return result;\n}\n\n/**\n * Resolve the value pointed to by a NodePath\n * e.g.\n * ```\n *  var a = 1;\n *  var b = a;\n *  b;\n * ```\n * `b.resolve()` will return `1`\n */\nexport function resolve(\n  this: NodePath,\n  dangerous?: boolean,\n  resolved?: NodePath[],\n) {\n  return _resolve.call(this, dangerous, resolved) || this;\n}\n\nexport function _resolve(\n  this: NodePath,\n  dangerous?: boolean,\n  resolved?: NodePath[],\n): NodePath | undefined | null {\n  // detect infinite recursion\n  // todo: possibly have a max length on this just to be safe\n  if (resolved?.includes(this)) return;\n\n  // we store all the paths we've \"resolved\" in this array to prevent infinite recursion\n  resolved = resolved || [];\n  resolved.push(this);\n\n  if (this.isVariableDeclarator()) {\n    if (this.get(\"id\").isIdentifier()) {\n      return this.get(\"init\").resolve(dangerous, resolved);\n    } else {\n      // otherwise it's a request for a pattern and that's a bit more tricky\n    }\n  } else if (this.isReferencedIdentifier()) {\n    const binding = this.scope.getBinding(this.node.name);\n    if (!binding) return;\n\n    // reassigned so we can't really resolve it\n    if (!binding.constant) return;\n\n    // todo - lookup module in dependency graph\n    if (binding.kind === \"module\") return;\n\n    if (binding.path !== this) {\n      const ret = binding.path.resolve(dangerous, resolved);\n      // If the identifier resolves to parent node then we can't really resolve it.\n      if (this.find(parent => parent.node === ret.node)) return;\n      return ret;\n    }\n  } else if (this.isTypeCastExpression()) {\n    // @ ts-ignore todo: babel-types\n    return this.get(\"expression\").resolve(dangerous, resolved);\n  } else if (dangerous && this.isMemberExpression()) {\n    // this is dangerous, as non-direct target assignments will mutate it's state\n    // making this resolution inaccurate\n\n    const targetKey = this.toComputedKey();\n    if (!isLiteral(targetKey)) return;\n\n    // @ts-expect-error todo(flow->ts): NullLiteral\n    const targetName = targetKey.value;\n\n    const target = this.get(\"object\").resolve(dangerous, resolved);\n\n    if (target.isObjectExpression()) {\n      const props = target.get(\"properties\");\n      for (const prop of props as any[]) {\n        if (!prop.isProperty()) continue;\n\n        const key = prop.get(\"key\");\n\n        // { foo: obj }\n        let match =\n          prop.isnt(\"computed\") && key.isIdentifier({ name: targetName });\n\n        // { \"foo\": \"obj\" } or { [\"foo\"]: \"obj\" }\n        match = match || key.isLiteral({ value: targetName });\n\n        if (match) return prop.get(\"value\").resolve(dangerous, resolved);\n      }\n    } else if (target.isArrayExpression() && !isNaN(+targetName)) {\n      const elems = target.get(\"elements\");\n      const elem = elems[targetName];\n      if (elem) return elem.resolve(dangerous, resolved);\n    }\n  }\n}\n\nexport function isConstantExpression(this: NodePath): boolean {\n  if (this.isIdentifier()) {\n    const binding = this.scope.getBinding(this.node.name);\n    if (!binding) return false;\n    return binding.constant;\n  }\n\n  if (this.isLiteral()) {\n    if (this.isRegExpLiteral()) {\n      return false;\n    }\n\n    if (this.isTemplateLiteral()) {\n      return this.get(\"expressions\").every(expression =>\n        expression.isConstantExpression(),\n      );\n    }\n\n    return true;\n  }\n\n  if (this.isUnaryExpression()) {\n    if (this.node.operator !== \"void\") {\n      return false;\n    }\n\n    return this.get(\"argument\").isConstantExpression();\n  }\n\n  if (this.isBinaryExpression()) {\n    const { operator } = this.node;\n    return (\n      operator !== \"in\" &&\n      operator !== \"instanceof\" &&\n      this.get(\"left\").isConstantExpression() &&\n      this.get(\"right\").isConstantExpression()\n    );\n  }\n\n  if (this.isMemberExpression()) {\n    return (\n      !this.node.computed &&\n      this.get(\"object\").isIdentifier({ name: \"Symbol\" }) &&\n      !this.scope.hasBinding(\"Symbol\", { noGlobals: true })\n    );\n  }\n\n  if (this.isCallExpression()) {\n    return (\n      this.node.arguments.length === 1 &&\n      this.get(\"callee\").matchesPattern(\"Symbol.for\") &&\n      !this.scope.hasBinding(\"Symbol\", { noGlobals: true }) &&\n      this.get(\"arguments\")[0].isStringLiteral()\n    );\n  }\n\n  return false;\n}\n\nexport function isInStrictMode(this: NodePath) {\n  const start = this.isProgram() ? this : this.parentPath;\n\n  const strictParent = start.find(path => {\n    if (path.isProgram({ sourceType: \"module\" })) return true;\n\n    if (path.isClass()) return true;\n\n    if (\n      path.isArrowFunctionExpression() &&\n      !path.get(\"body\").isBlockStatement()\n    ) {\n      return false;\n    }\n\n    let body: t.BlockStatement | t.Program;\n    if (path.isFunction()) {\n      body = path.node.body as t.BlockStatement;\n    } else if (path.isProgram()) {\n      // @ts-expect-error TODO: TS thinks that `path` here cannot be\n      // Program due to the `isProgram()` check at the beginning of\n      // the function\n      body = path.node;\n    } else {\n      return false;\n    }\n\n    for (const directive of body.directives) {\n      if (directive.value.value === \"use strict\") {\n        return true;\n      }\n    }\n  });\n\n  return !!strictParent;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,EAAA,GAAAC,OAAA;AAUsB;EATpBC,uBAAuB;EACvBC,YAAY;EACZC,gBAAgB;EAChBC,YAAY;EACZC,YAAY;EACZC,SAAS;EACTC,eAAe;EACfC,MAAM;EACNC,cAAc,EAAIC;AAAe,IAAAX,EAAA;AAW5B,SAASU,cAAcA,CAE5BE,OAAe,EACfC,YAAsB,EACb;EACT,OAAOF,eAAe,CAAC,IAAI,CAACG,IAAI,EAAEF,OAAO,EAAEC,YAAY,CAAC;AAC1D;AAE+C;EAM7CE,OAAO,CAACC,GAAG,GAAG,SAASA,GAAGA,CAExBC,GAAY,EACH;IAAA,IAAAC,UAAA;IACT,MAAMC,GAAG,IAAAD,UAAA,GAAI,IAAI,CAACJ,IAAI,qBAAVI,UAAA,CAAmBD,GAAG,CAAC;IACnC,IAAIE,GAAG,IAAIC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;MAC7B,OAAO,CAAC,CAACA,GAAG,CAACG,MAAM;IACrB,CAAC,MAAM;MACL,OAAO,CAAC,CAACH,GAAG;IACd;EACF,CAAC;AACH;AAEO,SAASI,QAAQA,CAAA,EAA0B;EAChD,OAAO,IAAI,CAACC,KAAK,CAACD,QAAQ,CAAC,IAAI,CAACT,IAAI,CAAC;AACvC;AAE+C;EAK7CC,OAAO,CAACU,EAAE,GAAGV,OAAO,CAACC,GAAG;EAMxBD,OAAO,CAACW,IAAI,GAAG,SAASA,IAAIA,CAE1BT,GAAY,EACH;IAET,OAAO,CAAC,IAAI,CAACD,GAAG,CAACC,GAAG,CAAC;EACvB,CAAC;EAMDF,OAAO,CAACY,MAAM,GAAG,SAASA,MAAMA,CAE9BV,GAAY,EACZW,KAAU,EACD;IACT,OAAQ,IAAI,CAACd,IAAI,CAAOG,GAAG,CAAC,KAAKW,KAAK;EACxC,CAAC;AACH;AAOO,SAASC,UAAUA,CAAiBC,IAAY,EAAW;EAChE,OAAOrB,MAAM,CAAC,IAAI,CAACqB,IAAI,EAAEA,IAAI,CAAC;AAChC;AAYO,SAASC,sCAAsCA,CAAA,EAAiB;EACrE,OACE,CAAC,IAAI,CAACd,GAAG,KAAK,MAAM,IAAI,IAAI,CAACA,GAAG,KAAK,MAAM,KAAK,IAAI,CAACe,UAAU,CAACC,KAAK,CAAC,CAAC;AAE3E;AAUO,SAASC,oCAAoCA,CAElDC,WAAmB,EACV;EACT,IAAI,IAAI,CAAClB,GAAG,KAAK,MAAM,IAAI,CAAC,IAAI,CAACe,UAAU,CAACI,yBAAyB,CAAC,CAAC,EAAE;IACvE,OAAO,KAAK;EACd;EAEA,IAAI,IAAI,CAAC/B,YAAY,CAAC,CAAC,EAAE;IACvB,OAAOD,gBAAgB,CAAC+B,WAAW,CAAC;EACtC,CAAC,MAAM,IAAI,IAAI,CAAC/B,gBAAgB,CAAC,CAAC,EAAE;IAClC,OAAOC,YAAY,CAAC8B,WAAW,CAAC;EAClC;EAEA,OAAO,KAAK;AACd;AAMO,SAASE,kBAAkBA,CAEhCC,mBAA6B,EACpB;EACT,IAAIC,IAAI,GAAG,IAAI;EACf,IAAIC,KAAK,GAAG,IAAI;EAEhB,GAAG;IACD,MAAM;MAAEV,IAAI;MAAEW;IAAU,CAAC,GAAGF,IAAI;IAGhC,IAAI,CAACC,KAAK,KAAKD,IAAI,CAACG,UAAU,CAAC,CAAC,IAAIZ,IAAI,KAAK,aAAa,CAAC,EAAE;MAC3D,OAAO,CAAC,CAACQ,mBAAmB;IAC9B;IAEAE,KAAK,GAAG,KAAK;IAIb,IAAIpB,KAAK,CAACC,OAAO,CAACoB,SAAS,CAAC,IAAIF,IAAI,CAACtB,GAAG,KAAKwB,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;MACjE,OAAO,KAAK;IACd;EACF,CAAC,QACC,CAACiB,IAAI,GAAGA,IAAI,CAACP,UAAU,KACvB,CAACO,IAAI,CAACI,SAAS,CAAC,CAAC,IACjB,CAACJ,IAAI,CAACK,cAAc,CAAC,CAAC;EAGxB,OAAO,IAAI;AACb;AAOO,SAASC,kBAAkBA,CAAA,EAA0B;EAC1D,IACE,IAAI,CAACb,UAAU,CAACc,kBAAkB,CAAC,CAAC,IACpC1C,gBAAgB,CAAC,IAAI,CAACqC,SAAmB,CAAC,EAC1C;IACA,OAAO,KAAK;EACd,CAAC,MAAM;IACL,OAAOvC,uBAAuB,CAAC6C,QAAQ,CAAC,IAAI,CAAC9B,GAAa,CAAC;EAC7D;AACF;AAMO,SAAS+B,gBAAgBA,CAE9BC,YAAoB,EACpBC,UAAkB,EACT;EACT,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAAC,CAAC,EAAE;IAClC,IACG,IAAI,CAACC,qBAAqB,CAAC,CAAC,IAC3B,IAAI,CAACtC,IAAI,CAACuC,QAAQ,CAACC,IAAI,KAAKJ,UAAU,IACvC,CAAC,IAAI,CAACK,kBAAkB,CAAC,CAAC,IAAI,IAAI,CAACC,0BAA0B,CAAC,CAAC,MAC7D,IAAI,CAAC1C,IAAI,CAAC2C,QAAQ,GACfjD,eAAe,CAAC,IAAI,CAACM,IAAI,CAACuC,QAAQ,EAAE;MAAEzB,KAAK,EAAEsB;IAAW,CAAC,CAAC,GACzD,IAAI,CAACpC,IAAI,CAACuC,QAAQ,CAAkBC,IAAI,KAAKJ,UAAU,CAAE,EAChE;MACA,MAAMQ,MAAM,GACV,IAAI,CACJC,GAAG,CAAC,QAAQ,CAAC;MACf,OACED,MAAM,CAACP,sBAAsB,CAAC,CAAC,IAC/BO,MAAM,CAACV,gBAAgB,CAACC,YAAY,EAAE,GAAG,CAAC;IAE9C;IAEA,OAAO,KAAK;EACd;EAEA,MAAMW,OAAO,GAAG,IAAI,CAACpC,KAAK,CAACqC,UAAU,CAAE,IAAI,CAAC/C,IAAI,CAAkBwC,IAAI,CAAC;EACvE,IAAI,CAACM,OAAO,IAAIA,OAAO,CAACE,IAAI,KAAK,QAAQ,EAAE,OAAO,KAAK;EAEvD,MAAMvB,IAAI,GAAGqB,OAAO,CAACrB,IAAI;EACzB,MAAMwB,MAAM,GAAGxB,IAAI,CAACP,UAAU;EAC9B,IAAI,CAAC+B,MAAM,CAACC,mBAAmB,CAAC,CAAC,EAAE,OAAO,KAAK;EAG/C,IAAID,MAAM,CAACjD,IAAI,CAACmD,MAAM,CAACrC,KAAK,KAAKqB,YAAY,EAAE;IAC7C,IAAI,CAACC,UAAU,EAAE,OAAO,IAAI;EAC9B,CAAC,MAAM;IACL,OAAO,KAAK;EACd;EAEA,IAAIX,IAAI,CAAC2B,wBAAwB,CAAC,CAAC,IAAIhB,UAAU,KAAK,SAAS,EAAE;IAC/D,OAAO,IAAI;EACb;EAEA,IAAIX,IAAI,CAAC4B,0BAA0B,CAAC,CAAC,IAAIjB,UAAU,KAAK,GAAG,EAAE;IAC3D,OAAO,IAAI;EACb;EAEA,IACEX,IAAI,CAAC6B,iBAAiB,CAAC,CAAC,IACxB9D,YAAY,CAACiC,IAAI,CAACzB,IAAI,CAACuD,QAAQ,EAAE;IAAEf,IAAI,EAAEJ;EAAW,CAAC,CAAC,EACtD;IACA,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAMO,SAASoB,SAASA,CAAA,EAAyB;EAChD,MAAMxD,IAAI,GAAG,IAAI,CAACA,IAAI;EACtB,IAAIA,IAAI,CAACyD,GAAG,EAAE;IACZ,MAAMC,IAAI,GAAG,IAAI,CAACC,GAAG,CAACC,OAAO,CAAC,CAAC;IAC/B,IAAIF,IAAI,EAAE,OAAOA,IAAI,CAACG,KAAK,CAAC7D,IAAI,CAAC8D,KAAK,EAAE9D,IAAI,CAACyD,GAAG,CAAC;EACnD;EACA,OAAO,EAAE;AACX;AAEO,SAASM,uBAAuBA,CAErCC,MAAgB,EACP;EACT,OAAO,IAAI,CAACC,+BAA+B,CAACD,MAAM,CAAC,KAAK,OAAO;AACjE;AAEA,SAASE,gBAAgBA,CAACzC,IAAc,EAAE;EACxC,OAAOA,IAAI,CAACI,SAAS,CAAC,CAAC,GACnBJ,IAAI,GACJ,CACEA,IAAI,CAACP,UAAU,CAACR,KAAK,CAACyD,iBAAiB,CAAC,CAAC,IACzC1C,IAAI,CAACP,UAAU,CAACR,KAAK,CAAC0D,gBAAgB,CAAC,CAAC,EACxC3C,IAAI;AACZ;AAEA,SAAS4C,oBAAoBA,CAACrD,IAAoB,EAAEb,GAAW,EAAE;EAC/D,QAAQa,IAAI;IAGV,KAAK,mBAAmB;MACtB,OAAOb,GAAG,KAAK,OAAO;IAIxB,KAAK,uBAAuB;IAC5B,KAAK,aAAa;MAChB,OAAOA,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,WAAW;IAGpD,KAAK,gBAAgB;IACrB,KAAK,kBAAkB;IACvB,KAAK,gBAAgB;IACrB,KAAK,gBAAgB;MACnB,OAAOA,GAAG,KAAK,MAAM;IAGvB,KAAK,cAAc;MACjB,OAAOA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,QAAQ;IAG3C,KAAK,iBAAiB;MACpB,OAAOA,GAAG,KAAK,OAAO;IAGxB,KAAK,cAAc;MACjB,OAAOA,GAAG,KAAK,SAAS;IAG1B,KAAK,mBAAmB;MACtB,OAAOA,GAAG,KAAK,OAAO;IAGxB,KAAK,0BAA0B;MAC7B,OAAOA,GAAG,KAAK,UAAU;IAG3B,KAAK,wBAAwB;MAC3B,OAAOA,GAAG,KAAK,WAAW;IAE5B;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAASmE,0BAA0BA,CAACC,KAAiB,EAAEC,QAAgB,EAAE;EACvE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,QAAQ,EAAEC,CAAC,EAAE,EAAE;IACjC,MAAMhD,IAAI,GAAG8C,KAAK,CAACE,CAAC,CAAC;IACrB,IAAIJ,oBAAoB,CAAC5C,IAAI,CAACwB,MAAM,CAACjC,IAAI,EAAES,IAAI,CAACiD,SAAS,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAYA,MAAMC,eAAe,GAAGC,MAAM,CAAC,CAAC;AAczB,SAASX,+BAA+BA,CAE7CD,MAAgB,EACS;EACzB,OAAOa,qCAAqC,CAAC,IAAI,EAAEb,MAAM,EAAE,IAAIc,GAAG,CAAC,CAAC,CAAC;AACvE;AAEA,SAASD,qCAAqCA,CAC5CE,IAAc,EACdf,MAAgB,EAChBgB,KAA2B,EACF;EAEzB,MAAMC,UAAU,GAAG;IACjBC,IAAI,EAAEhB,gBAAgB,CAACa,IAAI,CAAC;IAC5Bf,MAAM,EAAEE,gBAAgB,CAACF,MAAM;EACjC,CAAC;EAID,IAAIiB,UAAU,CAACjB,MAAM,CAAChE,IAAI,KAAKiF,UAAU,CAACC,IAAI,CAAClF,IAAI,EAAE;IACnD,OAAOmF,uDAAuD,CAC5DJ,IAAI,EACJE,UAAU,CAACjB,MAAM,EACjBgB,KACF,CAAC;EACH;EAEA,MAAMT,KAAK,GAAG;IACZP,MAAM,EAAEA,MAAM,CAACoB,WAAW,CAAC,CAAC;IAC5BF,IAAI,EAAEH,IAAI,CAACK,WAAW,CAAC;EACzB,CAAC;EAID,IAAIb,KAAK,CAACP,MAAM,CAAC/B,QAAQ,CAAC8C,IAAI,CAAC,EAAE,OAAO,OAAO;EAC/C,IAAIR,KAAK,CAACW,IAAI,CAACjD,QAAQ,CAAC+B,MAAM,CAAC,EAAE,OAAO,QAAQ;EAGhD,IAAIqB,UAAU;EACd,MAAMC,WAAW,GAAG;IAAEtB,MAAM,EAAE,CAAC;IAAEkB,IAAI,EAAE;EAAE,CAAC;EAE1C,OAAO,CAACG,UAAU,IAAIC,WAAW,CAACJ,IAAI,GAAGX,KAAK,CAACW,IAAI,CAAC1E,MAAM,EAAE;IAC1D,MAAMiB,IAAI,GAAG8C,KAAK,CAACW,IAAI,CAACI,WAAW,CAACJ,IAAI,CAAC;IACzCI,WAAW,CAACtB,MAAM,GAAGO,KAAK,CAACP,MAAM,CAACuB,OAAO,CAAC9D,IAAI,CAAC;IAC/C,IAAI6D,WAAW,CAACtB,MAAM,IAAI,CAAC,EAAE;MAC3BqB,UAAU,GAAG5D,IAAI;IACnB,CAAC,MAAM;MACL6D,WAAW,CAACJ,IAAI,EAAE;IACpB;EACF;EAEA,IAAI,CAACG,UAAU,EAAE;IACf,MAAM,IAAIG,KAAK,CACb,+CAA+C,GAC7C,8CACJ,CAAC;EACH;EAEA,IACElB,0BAA0B,CAACC,KAAK,CAACW,IAAI,EAAEI,WAAW,CAACJ,IAAI,GAAG,CAAC,CAAC,IAC5DZ,0BAA0B,CAACC,KAAK,CAACP,MAAM,EAAEsB,WAAW,CAACtB,MAAM,GAAG,CAAC,CAAC,EAChE;IACA,OAAO,SAAS;EAClB;EAEA,MAAMyB,UAAU,GAAG;IACjBP,IAAI,EAAEX,KAAK,CAACW,IAAI,CAACI,WAAW,CAACJ,IAAI,GAAG,CAAC,CAAC;IACtClB,MAAM,EAAEO,KAAK,CAACP,MAAM,CAACsB,WAAW,CAACtB,MAAM,GAAG,CAAC;EAC7C,CAAC;EAID,IACEyB,UAAU,CAACzB,MAAM,CAAC0B,OAAO,IACzBD,UAAU,CAACP,IAAI,CAACQ,OAAO,IACvBD,UAAU,CAACzB,MAAM,CAACrC,SAAS,KAAK8D,UAAU,CAACP,IAAI,CAACvD,SAAS,EACzD;IACA,OAAO8D,UAAU,CAACzB,MAAM,CAAC7D,GAAG,GAAGsF,UAAU,CAACP,IAAI,CAAC/E,GAAG,GAAG,QAAQ,GAAG,OAAO;EACzE;EAGA,MAAMwF,IAAI,GAAGtG,YAAY,CAACgG,UAAU,CAACrE,IAAI,CAAC;EAC1C,MAAM4E,WAAW,GAAG;IAClBV,IAAI,EAAES,IAAI,CAACJ,OAAO,CAACE,UAAU,CAACP,IAAI,CAACR,SAAS,CAAC;IAC7CV,MAAM,EAAE2B,IAAI,CAACJ,OAAO,CAACE,UAAU,CAACzB,MAAM,CAACU,SAAS;EAClD,CAAC;EACD,OAAOkB,WAAW,CAAC5B,MAAM,GAAG4B,WAAW,CAACV,IAAI,GAAG,QAAQ,GAAG,OAAO;AACnE;AAEA,SAASW,yDAAyDA,CAChEd,IAAc,EACdf,MAAgB,EAChBgB,KAA2B,EACF;EACzB,IAAI,CAAChB,MAAM,CAAC8B,qBAAqB,CAAC,CAAC,EAAE;IACnC,IACEjB,qCAAqC,CAACE,IAAI,EAAEf,MAAM,EAAEgB,KAAK,CAAC,KAAK,QAAQ,EACvE;MACA,OAAO,QAAQ;IACjB;IACA,OAAO,SAAS;EAClB,CAAC,MAAM,IAAIhB,MAAM,CAAC9C,UAAU,CAAC6E,mBAAmB,CAAC,CAAC,EAAE;IAClD,OAAO,SAAS;EAClB;EAMA,MAAMjD,OAAO,GAAGkB,MAAM,CAACtD,KAAK,CAACqC,UAAU,CAACiB,MAAM,CAAChE,IAAI,CAACgG,EAAE,CAACxD,IAAI,CAAC;EAG5D,IAAI,CAACM,OAAO,CAACmD,UAAU,EAAE,OAAO,QAAQ;EAExC,MAAMC,cAA+B,GAAGpD,OAAO,CAACoD,cAAc;EAE9D,IAAIC,SAAS;EAGb,KAAK,MAAM1E,IAAI,IAAIyE,cAAc,EAAE;IAGjC,MAAME,eAAe,GAAG,CAAC,CAAC3E,IAAI,CAAC4E,IAAI,CAAC5E,IAAI,IAAIA,IAAI,CAACzB,IAAI,KAAKgE,MAAM,CAAChE,IAAI,CAAC;IACtE,IAAIoG,eAAe,EAAE;IAErB,IAAI3E,IAAI,CAACtB,GAAG,KAAK,QAAQ,IAAI,CAACsB,IAAI,CAACP,UAAU,CAACoF,gBAAgB,CAAC,CAAC,EAAE;MAGhE,OAAO,SAAS;IAClB;IAEA,MAAMC,MAAM,GAAG1B,qCAAqC,CAACE,IAAI,EAAEtD,IAAI,EAAEuD,KAAK,CAAC;IAEvE,IAAImB,SAAS,IAAIA,SAAS,KAAKI,MAAM,EAAE;MACrC,OAAO,SAAS;IAClB,CAAC,MAAM;MACLJ,SAAS,GAAGI,MAAM;IACpB;EACF;EAEA,OAAOJ,SAAS;AAClB;AAEA,SAAShB,uDAAuDA,CAC9DJ,IAAc,EACdf,MAAgB,EAChBgB,KAA2B,EACF;EACzB,IAAIwB,OAAO,GAAGxB,KAAK,CAACnC,GAAG,CAACkC,IAAI,CAAC/E,IAAI,CAAC;EAClC,IAAIyG,MAAM;EAEV,IAAI,CAACD,OAAO,EAAE;IACZxB,KAAK,CAAC0B,GAAG,CAAC3B,IAAI,CAAC/E,IAAI,EAAGwG,OAAO,GAAG,IAAI1B,GAAG,CAAC,CAAE,CAAC;EAC7C,CAAC,MAAM,IAAK2B,MAAM,GAAGD,OAAO,CAAC3D,GAAG,CAACmB,MAAM,CAAChE,IAAI,CAAC,EAAG;IAC9C,IAAIyG,MAAM,KAAK9B,eAAe,EAAE;MAC9B,OAAO,SAAS;IAClB;IACA,OAAO8B,MAAM;EACf;EAEAD,OAAO,CAACE,GAAG,CAAC1C,MAAM,CAAChE,IAAI,EAAE2E,eAAe,CAAC;EAEzC,MAAMgC,MAAM,GAAGd,yDAAyD,CACtEd,IAAI,EACJf,MAAM,EACNgB,KACF,CAAC;EAEDwB,OAAO,CAACE,GAAG,CAAC1C,MAAM,CAAChE,IAAI,EAAE2G,MAAM,CAAC;EAChC,OAAOA,MAAM;AACf;AAYO,SAASC,OAAOA,CAErBC,SAAmB,EACnBC,QAAqB,EACrB;EACA,OAAOC,QAAQ,CAACC,IAAI,CAAC,IAAI,EAAEH,SAAS,EAAEC,QAAQ,CAAC,IAAI,IAAI;AACzD;AAEO,SAASC,QAAQA,CAEtBF,SAAmB,EACnBC,QAAqB,EACQ;EAAA,IAAAG,SAAA;EAG7B,KAAAA,SAAA,GAAIH,QAAQ,aAARG,SAAA,CAAUhF,QAAQ,CAAC,IAAI,CAAC,EAAE;EAG9B6E,QAAQ,GAAGA,QAAQ,IAAI,EAAE;EACzBA,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;EAEnB,IAAI,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAAE;IAC/B,IAAI,IAAI,CAACtE,GAAG,CAAC,IAAI,CAAC,CAACrD,YAAY,CAAC,CAAC,EAAE;MACjC,OAAO,IAAI,CAACqD,GAAG,CAAC,MAAM,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;IACtD,CAAC,MAAM,CAEP;EACF,CAAC,MAAM,IAAI,IAAI,CAACzE,sBAAsB,CAAC,CAAC,EAAE;IACxC,MAAMS,OAAO,GAAG,IAAI,CAACpC,KAAK,CAACqC,UAAU,CAAC,IAAI,CAAC/C,IAAI,CAACwC,IAAI,CAAC;IACrD,IAAI,CAACM,OAAO,EAAE;IAGd,IAAI,CAACA,OAAO,CAACsE,QAAQ,EAAE;IAGvB,IAAItE,OAAO,CAACE,IAAI,KAAK,QAAQ,EAAE;IAE/B,IAAIF,OAAO,CAACrB,IAAI,KAAK,IAAI,EAAE;MACzB,MAAM4F,GAAG,GAAGvE,OAAO,CAACrB,IAAI,CAACmF,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;MAErD,IAAI,IAAI,CAACT,IAAI,CAACpD,MAAM,IAAIA,MAAM,CAACjD,IAAI,KAAKqH,GAAG,CAACrH,IAAI,CAAC,EAAE;MACnD,OAAOqH,GAAG;IACZ;EACF,CAAC,MAAM,IAAI,IAAI,CAACC,oBAAoB,CAAC,CAAC,EAAE;IAEtC,OAAO,IAAI,CAACzE,GAAG,CAAC,YAAY,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC5D,CAAC,MAAM,IAAID,SAAS,IAAI,IAAI,CAACpE,kBAAkB,CAAC,CAAC,EAAE;IAIjD,MAAM8E,SAAS,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACtC,IAAI,CAAC/H,SAAS,CAAC8H,SAAS,CAAC,EAAE;IAG3B,MAAME,UAAU,GAAGF,SAAS,CAACzG,KAAK;IAElC,MAAMkD,MAAM,GAAG,IAAI,CAACnB,GAAG,CAAC,QAAQ,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;IAE9D,IAAI9C,MAAM,CAAC0D,kBAAkB,CAAC,CAAC,EAAE;MAC/B,MAAMC,KAAK,GAAG3D,MAAM,CAACnB,GAAG,CAAC,YAAY,CAAC;MACtC,KAAK,MAAM+E,IAAI,IAAID,KAAK,EAAW;QACjC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC,CAAC,EAAE;QAExB,MAAM1H,GAAG,GAAGyH,IAAI,CAAC/E,GAAG,CAAC,KAAK,CAAC;QAG3B,IAAIiF,KAAK,GACPF,IAAI,CAAChH,IAAI,CAAC,UAAU,CAAC,IAAIT,GAAG,CAACX,YAAY,CAAC;UAAEgD,IAAI,EAAEiF;QAAW,CAAC,CAAC;QAGjEK,KAAK,GAAGA,KAAK,IAAI3H,GAAG,CAACV,SAAS,CAAC;UAAEqB,KAAK,EAAE2G;QAAW,CAAC,CAAC;QAErD,IAAIK,KAAK,EAAE,OAAOF,IAAI,CAAC/E,GAAG,CAAC,OAAO,CAAC,CAAC+D,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;MAClE;IACF,CAAC,MAAM,IAAI9C,MAAM,CAAC+D,iBAAiB,CAAC,CAAC,IAAI,CAACC,KAAK,CAAC,CAACP,UAAU,CAAC,EAAE;MAC5D,MAAMQ,KAAK,GAAGjE,MAAM,CAACnB,GAAG,CAAC,UAAU,CAAC;MACpC,MAAMqF,IAAI,GAAGD,KAAK,CAACR,UAAU,CAAC;MAC9B,IAAIS,IAAI,EAAE,OAAOA,IAAI,CAACtB,OAAO,CAACC,SAAS,EAAEC,QAAQ,CAAC;IACpD;EACF;AACF;AAEO,SAASqB,oBAAoBA,CAAA,EAA0B;EAC5D,IAAI,IAAI,CAAC3I,YAAY,CAAC,CAAC,EAAE;IACvB,MAAMsD,OAAO,GAAG,IAAI,CAACpC,KAAK,CAACqC,UAAU,CAAC,IAAI,CAAC/C,IAAI,CAACwC,IAAI,CAAC;IACrD,IAAI,CAACM,OAAO,EAAE,OAAO,KAAK;IAC1B,OAAOA,OAAO,CAACsE,QAAQ;EACzB;EAEA,IAAI,IAAI,CAAC3H,SAAS,CAAC,CAAC,EAAE;IACpB,IAAI,IAAI,CAAC2I,eAAe,CAAC,CAAC,EAAE;MAC1B,OAAO,KAAK;IACd;IAEA,IAAI,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACxF,GAAG,CAAC,aAAa,CAAC,CAACyF,KAAK,CAACC,UAAU,IAC7CA,UAAU,CAACJ,oBAAoB,CAAC,CAClC,CAAC;IACH;IAEA,OAAO,IAAI;EACb;EAEA,IAAI,IAAI,CAACK,iBAAiB,CAAC,CAAC,EAAE;IAC5B,IAAI,IAAI,CAACxI,IAAI,CAACyI,QAAQ,KAAK,MAAM,EAAE;MACjC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAAC5F,GAAG,CAAC,UAAU,CAAC,CAACsF,oBAAoB,CAAC,CAAC;EACpD;EAEA,IAAI,IAAI,CAACO,kBAAkB,CAAC,CAAC,EAAE;IAC7B,MAAM;MAAED;IAAS,CAAC,GAAG,IAAI,CAACzI,IAAI;IAC9B,OACEyI,QAAQ,KAAK,IAAI,IACjBA,QAAQ,KAAK,YAAY,IACzB,IAAI,CAAC5F,GAAG,CAAC,MAAM,CAAC,CAACsF,oBAAoB,CAAC,CAAC,IACvC,IAAI,CAACtF,GAAG,CAAC,OAAO,CAAC,CAACsF,oBAAoB,CAAC,CAAC;EAE5C;EAEA,IAAI,IAAI,CAAC1F,kBAAkB,CAAC,CAAC,EAAE;IAC7B,OACE,CAAC,IAAI,CAACzC,IAAI,CAAC2C,QAAQ,IACnB,IAAI,CAACE,GAAG,CAAC,QAAQ,CAAC,CAACrD,YAAY,CAAC;MAAEgD,IAAI,EAAE;IAAS,CAAC,CAAC,IACnD,CAAC,IAAI,CAAC9B,KAAK,CAACiI,UAAU,CAAC,QAAQ,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;EAEzD;EAEA,IAAI,IAAI,CAACtC,gBAAgB,CAAC,CAAC,EAAE;IAC3B,OACE,IAAI,CAACtG,IAAI,CAAC6I,SAAS,CAACrI,MAAM,KAAK,CAAC,IAChC,IAAI,CAACqC,GAAG,CAAC,QAAQ,CAAC,CAACjD,cAAc,CAAC,YAAY,CAAC,IAC/C,CAAC,IAAI,CAACc,KAAK,CAACiI,UAAU,CAAC,QAAQ,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC,IACrD,IAAI,CAAC/F,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAACnD,eAAe,CAAC,CAAC;EAE9C;EAEA,OAAO,KAAK;AACd;AAEO,SAASoJ,cAAcA,CAAA,EAAiB;EAC7C,MAAMhF,KAAK,GAAG,IAAI,CAACjC,SAAS,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACX,UAAU;EAEvD,MAAM6H,YAAY,GAAGjF,KAAK,CAACuC,IAAI,CAAC5E,IAAI,IAAI;IACtC,IAAIA,IAAI,CAACI,SAAS,CAAC;MAAEmH,UAAU,EAAE;IAAS,CAAC,CAAC,EAAE,OAAO,IAAI;IAEzD,IAAIvH,IAAI,CAACwH,OAAO,CAAC,CAAC,EAAE,OAAO,IAAI;IAE/B,IACExH,IAAI,CAACH,yBAAyB,CAAC,CAAC,IAChC,CAACG,IAAI,CAACoB,GAAG,CAAC,MAAM,CAAC,CAACvD,gBAAgB,CAAC,CAAC,EACpC;MACA,OAAO,KAAK;IACd;IAEA,IAAI4J,IAAkC;IACtC,IAAIzH,IAAI,CAACG,UAAU,CAAC,CAAC,EAAE;MACrBsH,IAAI,GAAGzH,IAAI,CAACzB,IAAI,CAACkJ,IAAwB;IAC3C,CAAC,MAAM,IAAIzH,IAAI,CAACI,SAAS,CAAC,CAAC,EAAE;MAI3BqH,IAAI,GAAGzH,IAAI,CAACzB,IAAI;IAClB,CAAC,MAAM;MACL,OAAO,KAAK;IACd;IAEA,KAAK,MAAMmJ,SAAS,IAAID,IAAI,CAACE,UAAU,EAAE;MACvC,IAAID,SAAS,CAACrI,KAAK,CAACA,KAAK,KAAK,YAAY,EAAE;QAC1C,OAAO,IAAI;MACb;IACF;EACF,CAAC,CAAC;EAEF,OAAO,CAAC,CAACiI,YAAY;AACvB", "ignoreList": []}