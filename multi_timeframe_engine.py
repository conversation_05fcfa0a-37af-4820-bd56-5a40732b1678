#!/usr/bin/env python3
"""
Multi-Timeframe Analysis Engine for Quantum Trading System
Implements confluence detection across multiple timeframes
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import talib
import time
from performance_optimizer import performance_timer, cached_api_call, smart_cache

class MultiTimeframeAnalyzer:
    """Advanced multi-timeframe analysis with confluence detection"""
    
    def __init__(self, exchange):
        self.exchange = exchange
        self.timeframes = ['1m', '5m', '15m', '1h', '4h', '1d']
        self.timeframe_weights = {
            '1m': 0.1,   # Short-term noise
            '5m': 0.15,  # Entry timing
            '15m': 0.2,  # Short-term trend
            '1h': 0.25,  # Medium-term trend
            '4h': 0.2,   # Long-term trend
            '1d': 0.1    # Overall direction
        }
        self.confluence_threshold = 0.6  # 60% confluence required
    
    @performance_timer
    @cached_api_call(smart_cache, ttl_seconds=30)
    def fetch_ohlcv_data(self, symbol, timeframe, limit=100):
        """Fetch OHLCV data for specific timeframe"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            return df
        except Exception as e:
            print(f"[MTF ERROR] Failed to fetch {symbol} {timeframe}: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df):
        """Calculate technical indicators for a timeframe"""
        if len(df) < 20:
            return {}
        
        close = df['close'].values
        high = df['high'].values
        low = df['low'].values
        volume = df['volume'].values
        
        indicators = {}
        
        try:
            # Trend indicators
            indicators['sma_20'] = talib.SMA(close, timeperiod=20)
            indicators['sma_50'] = talib.SMA(close, timeperiod=50)
            indicators['ema_12'] = talib.EMA(close, timeperiod=12)
            indicators['ema_26'] = talib.EMA(close, timeperiod=26)
            
            # MACD
            macd, macd_signal, macd_hist = talib.MACD(close)
            indicators['macd'] = macd
            indicators['macd_signal'] = macd_signal
            indicators['macd_histogram'] = macd_hist
            
            # RSI
            indicators['rsi'] = talib.RSI(close, timeperiod=14)
            
            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20)
            indicators['bb_upper'] = bb_upper
            indicators['bb_middle'] = bb_middle
            indicators['bb_lower'] = bb_lower
            
            # Stochastic
            stoch_k, stoch_d = talib.STOCH(high, low, close)
            indicators['stoch_k'] = stoch_k
            indicators['stoch_d'] = stoch_d
            
            # ADX (trend strength)
            indicators['adx'] = talib.ADX(high, low, close, timeperiod=14)
            
            # Volume indicators
            indicators['volume_sma'] = talib.SMA(volume.astype(float), timeperiod=20)
            
        except Exception as e:
            print(f"[MTF ERROR] Indicator calculation failed: {e}")
        
        return indicators
    
    def analyze_trend_direction(self, df, indicators):
        """Analyze trend direction for a timeframe"""
        if len(df) < 20 or not indicators:
            return {'direction': 'NEUTRAL', 'strength': 0, 'confidence': 0}
        
        current_price = df['close'].iloc[-1]
        signals = []
        
        try:
            # SMA trend
            if 'sma_20' in indicators and 'sma_50' in indicators:
                sma_20 = indicators['sma_20'][-1]
                sma_50 = indicators['sma_50'][-1]
                if not (np.isnan(sma_20) or np.isnan(sma_50)):
                    if sma_20 > sma_50:
                        signals.append(('BULLISH', 0.7))
                    else:
                        signals.append(('BEARISH', 0.7))
            
            # MACD
            if 'macd' in indicators and 'macd_signal' in indicators:
                macd = indicators['macd'][-1]
                macd_signal = indicators['macd_signal'][-1]
                if not (np.isnan(macd) or np.isnan(macd_signal)):
                    if macd > macd_signal:
                        signals.append(('BULLISH', 0.6))
                    else:
                        signals.append(('BEARISH', 0.6))
            
            # RSI
            if 'rsi' in indicators:
                rsi = indicators['rsi'][-1]
                if not np.isnan(rsi):
                    if rsi > 70:
                        signals.append(('BEARISH', 0.5))  # Overbought
                    elif rsi < 30:
                        signals.append(('BULLISH', 0.5))  # Oversold
                    elif rsi > 50:
                        signals.append(('BULLISH', 0.3))
                    else:
                        signals.append(('BEARISH', 0.3))
            
            # Price vs SMA
            if 'sma_20' in indicators:
                sma_20 = indicators['sma_20'][-1]
                if not np.isnan(sma_20):
                    if current_price > sma_20:
                        signals.append(('BULLISH', 0.5))
                    else:
                        signals.append(('BEARISH', 0.5))
            
            # ADX for trend strength
            trend_strength = 0
            if 'adx' in indicators:
                adx = indicators['adx'][-1]
                if not np.isnan(adx):
                    trend_strength = min(adx / 50, 1.0)  # Normalize to 0-1
            
            # Calculate overall direction
            bullish_weight = sum(weight for direction, weight in signals if direction == 'BULLISH')
            bearish_weight = sum(weight for direction, weight in signals if direction == 'BEARISH')
            
            if bullish_weight > bearish_weight:
                direction = 'BULLISH'
                confidence = bullish_weight / (bullish_weight + bearish_weight)
            elif bearish_weight > bullish_weight:
                direction = 'BEARISH'
                confidence = bearish_weight / (bullish_weight + bearish_weight)
            else:
                direction = 'NEUTRAL'
                confidence = 0.5
            
            return {
                'direction': direction,
                'strength': trend_strength,
                'confidence': confidence,
                'signals_count': len(signals)
            }
            
        except Exception as e:
            print(f"[MTF ERROR] Trend analysis failed: {e}")
            return {'direction': 'NEUTRAL', 'strength': 0, 'confidence': 0}
    
    def detect_support_resistance(self, df):
        """Detect key support and resistance levels"""
        if len(df) < 20:
            return {'support': [], 'resistance': []}
        
        try:
            highs = df['high'].values
            lows = df['low'].values
            
            # Find local peaks and troughs
            from scipy.signal import argrelextrema
            
            # Resistance levels (local maxima)
            resistance_indices = argrelextrema(highs, np.greater, order=5)[0]
            resistance_levels = [highs[i] for i in resistance_indices[-5:]]  # Last 5 resistance levels
            
            # Support levels (local minima)
            support_indices = argrelextrema(lows, np.less, order=5)[0]
            support_levels = [lows[i] for i in support_indices[-5:]]  # Last 5 support levels
            
            return {
                'support': sorted(support_levels),
                'resistance': sorted(resistance_levels, reverse=True)
            }
            
        except Exception as e:
            print(f"[MTF ERROR] S/R detection failed: {e}")
            return {'support': [], 'resistance': []}
    
    @performance_timer
    def analyze_symbol_multi_timeframe(self, symbol):
        """Comprehensive multi-timeframe analysis for a symbol"""
        print(f"🔍 Analyzing {symbol} across multiple timeframes...")
        
        timeframe_analysis = {}
        
        for timeframe in self.timeframes:
            print(f"   📊 Analyzing {timeframe}...")
            
            # Fetch data
            df = self.fetch_ohlcv_data(symbol, timeframe)
            if df.empty:
                continue
            
            # Calculate indicators
            indicators = self.calculate_technical_indicators(df)
            
            # Analyze trend
            trend_analysis = self.analyze_trend_direction(df, indicators)
            
            # Detect S/R levels
            sr_levels = self.detect_support_resistance(df)
            
            timeframe_analysis[timeframe] = {
                'trend': trend_analysis,
                'support_resistance': sr_levels,
                'current_price': df['close'].iloc[-1],
                'volume': df['volume'].iloc[-1],
                'indicators': {
                    'rsi': indicators.get('rsi', [np.nan])[-1] if 'rsi' in indicators else np.nan,
                    'macd': indicators.get('macd', [np.nan])[-1] if 'macd' in indicators else np.nan,
                    'adx': indicators.get('adx', [np.nan])[-1] if 'adx' in indicators else np.nan
                }
            }
        
        return timeframe_analysis
    
    def calculate_confluence_score(self, timeframe_analysis):
        """Calculate confluence score across timeframes"""
        if not timeframe_analysis:
            return {'score': 0, 'direction': 'NEUTRAL', 'details': {}}
        
        bullish_score = 0
        bearish_score = 0
        total_weight = 0
        
        details = {}
        
        for timeframe, analysis in timeframe_analysis.items():
            if 'trend' not in analysis:
                continue
            
            weight = self.timeframe_weights.get(timeframe, 0.1)
            trend = analysis['trend']
            direction = trend.get('direction', 'NEUTRAL')
            confidence = trend.get('confidence', 0)
            
            weighted_score = weight * confidence
            total_weight += weight
            
            if direction == 'BULLISH':
                bullish_score += weighted_score
            elif direction == 'BEARISH':
                bearish_score += weighted_score
            
            details[timeframe] = {
                'direction': direction,
                'confidence': confidence,
                'weight': weight,
                'weighted_score': weighted_score
            }
        
        if total_weight == 0:
            return {'score': 0, 'direction': 'NEUTRAL', 'details': details}
        
        # Normalize scores
        bullish_score /= total_weight
        bearish_score /= total_weight
        
        if bullish_score > bearish_score:
            overall_direction = 'BULLISH'
            confluence_score = bullish_score
        elif bearish_score > bullish_score:
            overall_direction = 'BEARISH'
            confluence_score = bearish_score
        else:
            overall_direction = 'NEUTRAL'
            confluence_score = 0.5
        
        return {
            'score': confluence_score,
            'direction': overall_direction,
            'bullish_score': bullish_score,
            'bearish_score': bearish_score,
            'meets_threshold': confluence_score >= self.confluence_threshold,
            'details': details
        }
    
    def get_trading_recommendation(self, symbol):
        """Get comprehensive trading recommendation"""
        print(f"🎯 Generating trading recommendation for {symbol}...")
        
        # Multi-timeframe analysis
        mtf_analysis = self.analyze_symbol_multi_timeframe(symbol)
        
        # Calculate confluence
        confluence = self.calculate_confluence_score(mtf_analysis)
        
        # Generate recommendation
        recommendation = {
            'symbol': symbol,
            'timestamp': datetime.now().isoformat(),
            'confluence': confluence,
            'timeframe_analysis': mtf_analysis,
            'recommendation': 'HOLD'  # Default
        }
        
        if confluence['meets_threshold']:
            if confluence['direction'] == 'BULLISH':
                recommendation['recommendation'] = 'BUY'
            elif confluence['direction'] == 'BEARISH':
                recommendation['recommendation'] = 'SELL'
        
        # Add risk assessment
        current_price = None
        for tf_data in mtf_analysis.values():
            if 'current_price' in tf_data:
                current_price = tf_data['current_price']
                break
        
        if current_price:
            # Find nearest support/resistance for risk management
            all_support = []
            all_resistance = []
            
            for tf_data in mtf_analysis.values():
                if 'support_resistance' in tf_data:
                    all_support.extend(tf_data['support_resistance'].get('support', []))
                    all_resistance.extend(tf_data['support_resistance'].get('resistance', []))
            
            # Find closest levels
            if all_support:
                closest_support = max([s for s in all_support if s < current_price], default=None)
            else:
                closest_support = None
            
            if all_resistance:
                closest_resistance = min([r for r in all_resistance if r > current_price], default=None)
            else:
                closest_resistance = None
            
            recommendation['risk_levels'] = {
                'current_price': current_price,
                'nearest_support': closest_support,
                'nearest_resistance': closest_resistance
            }
        
        return recommendation
    
    def print_recommendation(self, recommendation):
        """Print formatted recommendation"""
        print("\n" + "="*60)
        print(f"📈 MULTI-TIMEFRAME ANALYSIS: {recommendation['symbol']}")
        print("="*60)
        
        confluence = recommendation['confluence']
        print(f"🎯 CONFLUENCE SCORE: {confluence['score']:.2%}")
        print(f"📊 OVERALL DIRECTION: {confluence['direction']}")
        print(f"✅ MEETS THRESHOLD: {confluence['meets_threshold']}")
        print(f"🚀 RECOMMENDATION: {recommendation['recommendation']}")
        
        if 'risk_levels' in recommendation:
            risk = recommendation['risk_levels']
            print(f"\n💰 CURRENT PRICE: {risk['current_price']:.4f}")
            if risk['nearest_support']:
                print(f"🛡️ NEAREST SUPPORT: {risk['nearest_support']:.4f}")
            if risk['nearest_resistance']:
                print(f"🚧 NEAREST RESISTANCE: {risk['nearest_resistance']:.4f}")
        
        print(f"\n📋 TIMEFRAME BREAKDOWN:")
        for tf, details in confluence['details'].items():
            print(f"   {tf}: {details['direction']} ({details['confidence']:.2%} confidence)")
        
        print("="*60)

if __name__ == "__main__":
    # Test the multi-timeframe analyzer
    try:
        exchange = ccxt.binance({
            'apiKey': 'your_api_key',
            'secret': 'your_secret',
            'sandbox': True,
            'enableRateLimit': True,
        })
        
        analyzer = MultiTimeframeAnalyzer(exchange)
        recommendation = analyzer.get_trading_recommendation('BTC/USDT')
        analyzer.print_recommendation(recommendation)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Please configure your API keys to test the multi-timeframe analyzer")
